'use client';

import { useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { QueryKeys } from '@/lib/query-keys';
import { useTenantUuid } from '@/hooks/useTenantUuid';
import { useRealtimeConnectionStatus } from './useRealtimeConnectionStatus';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';
import { deduplicateMessages, type Message } from './message-dedup';

interface FallbackOptions {
  tenantId: string;
  onUpdateDetected?: (ticketId: string, changes: Partial<Ticket>) => void;
}

/**
 * Smart fallback mechanism for when real-time is unavailable
 * - Shows cached data instantly
 * - Validates in background without blocking UI
 * - Only updates changed fields incrementally
 */
export function useSmartFallback({
  tenantId,
  onUpdateDetected,
}: FallbackOptions) {
  const queryClient = useQueryClient();
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;
  const connectionStatus = useRealtimeConnectionStatus(tenantId);

  // Track ongoing validations to prevent duplicates
  const validationInProgress = useRef<Set<string>>(new Set());
  // CRITICAL FIX: Track message validations separately to prevent duplicate API calls
  const messageValidationInProgress = useRef<Set<string>>(new Set());

  const validateMessagesInBackground = useCallback(
    async (ticketId: string) => {
      if (!tenantUuid) return;

      // CRITICAL FIX: Prevent concurrent message validation for same ticket
      const messageKey = `${tenantUuid}-${ticketId}`;
      if (messageValidationInProgress.current.has(messageKey)) {
        console.log(
          '⏭️ Message validation already in progress for ticket:',
          ticketId
        );
        return;
      }

      // Mark message validation as in progress
      messageValidationInProgress.current.add(messageKey);

      try {
        const cachedMessages = queryClient.getQueryData(
          QueryKeys.TICKETS.messages(tenantUuid, ticketId)
        ) as
          | Array<{
              id: string;
              content: string;
              authorId: string;
              authorName: string;
              createdAt: Date;
              ticketId: string;
              authorEmail?: string;
              authorAvatar?: string | null;
              attachments: Array<{
                id: string;
                name: string;
                type: string;
                size: number;
                url: string;
                uploadedAt: Date;
              }>;
            }>
          | undefined;

        const response = await fetch(
          `/api/tickets/${ticketId}/messages?tenant_id=${tenantUuid}`,
          {
            signal: AbortSignal.timeout(5000),
          }
        );

        if (!response.ok) return;

        const { messages: freshMessages } = await response.json();

        // CRITICAL FIX: Use more sophisticated comparison to prevent unnecessary updates
        // Check message count AND last message timestamp to avoid race condition updates
        const cachedCount = cachedMessages?.length || 0;
        const freshCount = freshMessages.length;

        let shouldUpdate = cachedCount !== freshCount;

        // If counts match, check if the last message is different (handles updates to existing messages)
        if (!shouldUpdate && cachedMessages && freshMessages.length > 0) {
          const cachedLast = cachedMessages[cachedMessages.length - 1];
          const freshLast = freshMessages[freshMessages.length - 1];

          if (cachedLast && freshLast) {
            const cachedTime = new Date(cachedLast.createdAt).getTime();
            const freshTime = new Date(
              freshLast.createdAt || freshLast.created_at
            ).getTime();
            shouldUpdate =
              cachedLast.id !== freshLast.id || cachedTime !== freshTime;
          }
        }

        if (shouldUpdate) {
          console.log('📨 Updating messages cache for ticket:', ticketId, {
            cachedCount,
            freshCount,
            reason:
              cachedCount !== freshCount
                ? 'count_changed'
                : 'last_message_changed',
          });

          // CRITICAL FIX: Apply deduplication when updating cache to prevent race condition duplicates
          queryClient.setQueryData(
            QueryKeys.TICKETS.messages(tenantUuid, ticketId),
            (oldMessages: Message[]) => {
              if (!oldMessages || oldMessages.length === 0) {
                return deduplicateMessages(freshMessages);
              }

              // CRITICAL FIX: Merge cached and fresh messages, then deduplicate
              // This handles race conditions where multiple API calls return overlapping data
              const combined = [...oldMessages, ...freshMessages];
              const deduplicated = deduplicateMessages(combined);

              console.log('🔄 Deduplication result:', {
                ticketId,
                oldCount: oldMessages.length,
                freshCount: freshMessages.length,
                combinedCount: combined.length,
                deduplicatedCount: deduplicated.length,
                duplicatesRemoved: combined.length - deduplicated.length,
              });

              return deduplicated;
            }
          );
        } else {
          console.log(
            '✅ Messages cache already up-to-date for ticket:',
            ticketId
          );
        }
      } catch (error) {
        console.warn(
          '⚠️ Message validation failed for ticket:',
          ticketId,
          error
        );
        // Silent fail
      } finally {
        // CRITICAL FIX: Always remove from in-progress set
        messageValidationInProgress.current.delete(messageKey);
      }
    },
    [tenantUuid, queryClient]
  );

  const validateTicketInBackground = useCallback(
    async (ticketId: string) => {
      // Skip if validation already in progress
      if (validationInProgress.current.has(ticketId)) {
        return;
      }

      // Check for debug mode to force fallback
      const forceDebugFallback =
        typeof window !== 'undefined' &&
        window.localStorage.getItem('DEBUG_FORCE_FALLBACK') === 'true';

      // Only skip if explicitly connected AND not in debug mode
      if (connectionStatus?.isConnected === true && !forceDebugFallback) {
        return;
      }

      if (!tenantUuid) return;

      // Mark validation as in progress
      validationInProgress.current.add(ticketId);

      try {
        // Get current cached data - check detail first, then look in list
        let cachedTicket = queryClient.getQueryData(
          QueryKeys.TICKETS.detail(tenantUuid, ticketId)
        ) as Ticket | undefined;

        // If no detail cache, try to find in the list cache
        if (!cachedTicket) {
          const listQueryKey = QueryKeys.TICKETS.list(tenantUuid);
          const cachedList = queryClient.getQueryData(listQueryKey) as
            | Ticket[]
            | undefined;

          if (cachedList) {
            cachedTicket = cachedList.find((t) => t.id === ticketId);
          }

          // If still no cached data, continue to fetch
          if (!cachedTicket) {
            // Will fetch fresh data
          }
        }

        // Fetch fresh data in background
        const response = await fetch(
          `/api/tickets/${ticketId}?tenant_id=${tenantUuid}`,
          {
            // Add timeout to prevent hanging
            signal: AbortSignal.timeout(5000),
          }
        );

        if (!response.ok) {
          if (response.status === 404) {
            // Ticket deleted - remove from cache
            queryClient.removeQueries({
              queryKey: QueryKeys.TICKETS.detail(tenantUuid, ticketId),
            });
          }
          return;
        }

        const { data: freshTicket } = await response.json();

        if (!cachedTicket) {
          // No cached data - store the fresh data
          queryClient.setQueryData(
            QueryKeys.TICKETS.detail(tenantUuid, ticketId),
            freshTicket
          );

          // Also update in the list view
          queryClient.setQueriesData(
            { queryKey: ['tickets', tenantUuid], exact: false },
            (oldData: Ticket[] | undefined) => {
              if (!oldData || !Array.isArray(oldData)) return oldData;

              const exists = oldData.some((ticket) => ticket.id === ticketId);
              if (!exists) {
                return [...oldData, freshTicket];
              }

              return oldData.map((ticket) =>
                ticket.id === ticketId ? freshTicket : ticket
              );
            }
          );
        } else {
          // Compare and find changes
          const changes = detectChanges(cachedTicket, freshTicket);

          if (Object.keys(changes).length > 0) {
            // Update only the changed fields
            queryClient.setQueryData(
              QueryKeys.TICKETS.detail(tenantUuid, ticketId),
              (old: Ticket | undefined) => {
                if (!old) return freshTicket;
                return { ...old, ...changes, updatedAt: freshTicket.updatedAt };
              }
            );

            // Also update in the list view
            queryClient.setQueriesData(
              { queryKey: ['tickets', tenantUuid], exact: false },
              (oldData: Ticket[] | undefined) => {
                if (!oldData || !Array.isArray(oldData)) return oldData;

                return oldData.map((ticket) =>
                  ticket.id === ticketId
                    ? {
                        ...ticket,
                        ...changes,
                        updatedAt: freshTicket.updatedAt,
                      }
                    : ticket
                );
              }
            );

            // Notify about updates if callback provided
            onUpdateDetected?.(ticketId, changes);
          }
        }

        // CRITICAL FIX: Only validate messages when necessary to prevent race conditions
        // Check if messages are missing or potentially outdated before validating
        const shouldValidateMessages =
          freshTicket &&
          (() => {
            const cachedMessages = queryClient.getQueryData(
              QueryKeys.TICKETS.messages(tenantUuid, ticketId)
            );

            // Always validate if no cached messages exist
            if (!cachedMessages) {
              return true;
            }

            // Don't validate if messages were just updated (within last 5 seconds)
            const messageKey = `${tenantUuid}-${ticketId}`;
            if (messageValidationInProgress.current.has(messageKey)) {
              return false;
            }

            // Only validate if ticket was updated recently (might have new messages)
            const ticketUpdated = new Date(freshTicket.updatedAt).getTime();
            const now = Date.now();
            const fiveMinutesAgo = now - 5 * 60 * 1000;

            return ticketUpdated > fiveMinutesAgo;
          })();

        if (shouldValidateMessages) {
          console.log(
            '🔄 Fallback: Validating messages for recently updated ticket:',
            ticketId
          );
          await validateMessagesInBackground(ticketId);
        } else {
          console.log(
            '⏭️ Skipping message validation for ticket:',
            ticketId,
            'not recently updated'
          );
        }
      } catch {
        // Silent fail - user still sees cached data
      } finally {
        // Remove from in-progress set
        validationInProgress.current.delete(ticketId);
      }
    },
    [
      tenantUuid,
      queryClient,
      connectionStatus,
      onUpdateDetected,
      validateMessagesInBackground,
    ]
  );

  return {
    validateTicketInBackground,
    isRealtimeDown: !connectionStatus?.isConnected,
  };
}

/**
 * Detect changes between cached and fresh ticket data
 */
function detectChanges(cached: Ticket, fresh: Ticket): Partial<Ticket> {
  const changes: Partial<Ticket> = {};

  // Check key fields that might change
  const fieldsToCheck: (keyof Ticket)[] = [
    'status',
    'priority',
    'department',
    'assignedTo',
    'assignedToClerkId',
    'title',
    'description',
    'resolvedAt',
    'closedAt',
  ];

  fieldsToCheck.forEach((field) => {
    const cachedValue = cached[field];
    const freshValue = fresh[field];
    if (cachedValue !== freshValue) {
      // Use Object.assign to avoid type issues
      Object.assign(changes, { [field]: freshValue });
    }
  });

  // Check if updated time changed
  if (
    new Date(cached.updatedAt).getTime() !== new Date(fresh.updatedAt).getTime()
  ) {
    changes.updatedAt = fresh.updatedAt;
  }

  return changes;
}
