'use client';

import { useRef, useMemo, useEffect, memo, useCallback } from 'react';
import { CreateTicketFormData } from '../models/ticket-form.schema';
import { Button } from '@/features/shared/components/ui/button';
import { Input } from '@/features/shared/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
} from '@/features/shared/components/ui/form';
import { UserAutocomplete } from '@/features/shared/components';
import { DynamicRichTextEditor } from '@/features/shared/components/DynamicRichTextEditor';
import { DynamicFileUpload } from '@/features/shared/components/DynamicFileUpload';
import { cn, countWords } from '@/lib/utils';
import { Send, Trash2 } from 'lucide-react';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useCreateTicket } from '../hooks/useCreateTicket';
import { priorityConfig, formatTicketPriority } from '../config/ticket-options';
import { useActiveDepartments } from '@/features/departments/hooks/useDepartments';
import { useTenant } from '@/features/tenant/store/use-tenant-store';
import { useTenantUuid } from '@/hooks/useTenantUuid';
import { format } from 'date-fns';

interface CreateTicketFormProps {
  onSubmit: (
    data: CreateTicketFormData & { attachment_ids: string[] }
  ) => Promise<void>;
  onDiscard?: () => void;
  isSubmitting?: boolean;
  tenantId?: string | undefined;
  onFormReady?: () => void; // Callback when form is fully rendered and ready
}

const DropdownOption = memo(
  ({
    config,
  }: {
    config: { color: string; dotColor: string; label: string };
  }) => (
    <div className='flex items-center gap-2 text-xs text-black dark:text-gray-100'>
      <div className={cn('w-1.5 h-1.5 mr-1 rounded-full', config.dotColor)} />
      <span className='text-black dark:text-gray-100'>{config.label}</span>
    </div>
  )
);

export const CreateTicketForm = memo(function CreateTicketForm({
  onSubmit,
  onDiscard,
  isSubmitting = false,
  tenantId,
  onFormReady,
}: CreateTicketFormProps) {
  const { role } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { tenantId: currentTenantId } = useTenant();
  const tenantUuidQuery = useTenantUuid(currentTenantId || '');
  const tenantUuid = tenantUuidQuery.data;

  // Fetch active departments for the dropdown
  const { data: activeDepartments, validateInBackground } =
    useActiveDepartments(tenantUuid || null);

  const {
    form,
    uploadedFiles,
    setUploadedFiles,
    handleSubmit,
    handleDiscard: discard,
  } = useCreateTicket(tenantId ?? null, onSubmit, role);

  const canUseAdvancedFields = role === 'super_admin' || role === 'admin';

  // Auto-selection logic removed - using placeholder states instead

  // Watch form fields for validation
  const watchedTitle = form.watch('title');
  const watchedDescription = form.watch('description');

  // Calculate if button should be enabled based on meaningful content
  const isFormValid = useMemo(() => {
    const titleWordCount = countWords(watchedTitle || '');
    const descriptionWordCount = countWords(watchedDescription || '');

    // Enable button if either title or description has at least 5 meaningful words
    return titleWordCount >= 5 || descriptionWordCount >= 5;
  }, [watchedTitle, watchedDescription]);

  const handleAttachClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleDiscard = useCallback(() => {
    discard();
    onDiscard?.();
  }, [discard, onDiscard]);

  // Modern React 19 pattern: Signal readiness immediately after render
  useEffect(() => onFormReady?.(), [onFormReady]);

  return (
    <div className='flex-1 flex flex-col overflow-hidden'>
      <div className='flex-1 overflow-auto'>
        {/* Form Content - Scrollable */}
        <div className='flex-1 overflow-y-auto min-h-0'>
          <div className='p-6'>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className='space-y-4'
              >
                {/* Header */}
                <div className='border-b border-gray-200 dark:border-gray-700 pb-4 mb-4'>
                  <div className='flex items-center justify-between'>
                    <h1 className='text-xl font-semibold text-gray-900 dark:text-gray-100'>
                      Create New Ticket
                    </h1>
                    <div className='flex items-center gap-2 custom-dropdowns'>
                      {/* Priority Dropdown */}
                      <FormField
                        control={form.control}
                        name='priority'
                        render={({ field }) => (
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger
                              className={cn(
                                'inline-flex items-center justify-center rounded-md h-6 px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 transition-[color,box-shadow] overflow-hidden',
                                'border bg-white dark:bg-gray-800 text-black dark:text-gray-100',
                                'hover:opacity-80 cursor-pointer'
                              )}
                            >
                              <SelectValue placeholder='Select Priority'>
                                <div className='flex items-center'>
                                  {field.value ? (
                                    // Selected value: show only priority's own colored dot
                                    <>
                                      <div
                                        className={cn(
                                          'w-1.5 h-1.5 rounded-full mr-1',
                                          priorityConfig[
                                            field.value as keyof typeof priorityConfig
                                          ]?.dotColor
                                        )}
                                      />
                                      <span className='text-black dark:text-gray-100'>
                                        {formatTicketPriority(
                                          field.value as
                                            | 'low'
                                            | 'medium'
                                            | 'high'
                                            | 'urgent'
                                        )}
                                      </span>
                                    </>
                                  ) : (
                                    // Placeholder: show black dot before text
                                    <>
                                      <span className='text-black dark:text-gray-100 mr-1'>
                                        ●
                                      </span>
                                      <span className='text-black dark:text-gray-100'>
                                        Select Priority
                                      </span>
                                    </>
                                  )}
                                </div>
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {Object.entries(priorityConfig).map(
                                ([value, config]) => (
                                  <SelectItem key={value} value={value}>
                                    <DropdownOption config={config} />
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        )}
                      />

                      {/* Department Dropdown */}
                      <FormField
                        control={form.control}
                        name='department'
                        render={({ field }) => (
                          <Select
                            value={field.value}
                            onValueChange={field.onChange}
                          >
                            <SelectTrigger
                              className={cn(
                                'inline-flex items-center justify-center rounded-md h-6 px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 gap-1 transition-[color,box-shadow] overflow-hidden',
                                'border bg-white dark:bg-gray-800 text-black dark:text-gray-100',
                                'hover:opacity-80 cursor-pointer'
                              )}
                              onMouseEnter={validateInBackground}
                            >
                              <SelectValue placeholder='Select Department'>
                                <div className='flex items-center'>
                                  {activeDepartments?.find(
                                    (dept) => dept.id === field.value
                                  ) ? (
                                    // Selected value: show only department's own colored dot
                                    <>
                                      <div
                                        className={cn(
                                          'w-1.5 h-1.5 mr-1 rounded-full',
                                          activeDepartments.find(
                                            (dept) => dept.id === field.value
                                          )?.dot_color
                                        )}
                                      />
                                      <span className='text-black dark:text-gray-100'>
                                        {
                                          activeDepartments.find(
                                            (dept) => dept.id === field.value
                                          )?.name
                                        }{' '}
                                        Department
                                      </span>
                                    </>
                                  ) : (
                                    // Placeholder: show black dot before text
                                    <>
                                      <span className='text-black dark:text-gray-100 mr-1'>
                                        ●
                                      </span>
                                      <span className='text-black dark:text-gray-100'>
                                        Select Department
                                      </span>
                                    </>
                                  )}
                                </div>
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {activeDepartments?.map((department) => (
                                <SelectItem
                                  key={department.id}
                                  value={department.id}
                                >
                                  <div className='flex items-center gap-2 text-xs text-black dark:text-gray-100'>
                                    <div
                                      className={cn(
                                        'w-1.5 h-1.5 rounded-full',
                                        department.dot_color
                                      )}
                                    />
                                    <span className='text-black dark:text-gray-100'>
                                      {department.name} Department
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  </div>
                </div>
                {/* Assign To Field - Only for Admins and Super Admins */}
                {canUseAdvancedFields && (
                  <FormField
                    control={form.control}
                    name='assignedTo'
                    render={({ field }) => (
                      <FormItem>
                        <div className='flex items-center justify-between'>
                          <div className='flex items-center gap-2'>
                            <FormLabel className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                              Assign To (Admins & Agents)
                            </FormLabel>
                            <span className='text-xs text-gray-500 dark:text-gray-400'>
                              {format(new Date(), 'MMM d, yyyy')}
                            </span>
                          </div>
                          {!field.value && (
                            <p className='text-xs text-muted-foreground'>
                              💡 Leave empty to apply auto-assignment rules
                              based on department
                            </p>
                          )}
                        </div>
                        <UserAutocomplete
                          value={field.value || ''}
                          onChange={field.onChange}
                          placeholder='Select user to assign...'
                          roleFilter={['admin', 'agent']}
                          multiple={false}
                          dropdownOnly={true}
                          returnUserIds={true}
                          className='h-10 no-focus-ring'
                        />
                      </FormItem>
                    )}
                  />
                )}

                {/* CC Field - Only for Admins and Super Admins */}
                {canUseAdvancedFields && (
                  <FormField
                    control={form.control}
                    name='cc'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                          CC
                        </FormLabel>
                        <UserAutocomplete
                          value={field.value || []}
                          onChange={field.onChange}
                          placeholder='Type email to search users or enter email...'
                          roleFilter={['admin', 'agent']}
                          multiple={true}
                          dropdownOnly={true}
                          className='h-10 no-focus-ring'
                        />
                      </FormItem>
                    )}
                  />
                )}

                {/* Title Field */}
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className='text-sm font-medium text-gray-700 dark:text-gray-300'>
                        Title *
                      </FormLabel>
                      <Input
                        placeholder='Brief description of the issue'
                        className='h-10'
                        {...field}
                      />
                    </FormItem>
                  )}
                />

                {/* Description Field */}
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <DynamicRichTextEditor
                        value={field.value}
                        onChange={field.onChange}
                        placeholder='Describe the issue in detail...'
                        disabled={isSubmitting}
                        onAttachClick={handleAttachClick}
                      />
                    </FormItem>
                  )}
                />

                {/* File Upload */}
                <DynamicFileUpload
                  files={uploadedFiles}
                  onFilesChange={setUploadedFiles}
                  disabled={isSubmitting}
                  fileInputRef={fileInputRef}
                />

                {/* Submit buttons */}
                <div className='flex items-center justify-start gap-3'>
                  <Button
                    type='submit'
                    disabled={isSubmitting || !isFormValid}
                    className='rounded-lg px-4 py-2 font-medium text-sm transition-all duration-200'
                  >
                    <Send className='h-4 w-4 mr-2' />
                    {isSubmitting ? 'Creating...' : 'Create Ticket'}
                  </Button>
                  <Button
                    type='button'
                    variant='outline'
                    onClick={handleDiscard}
                    disabled={isSubmitting}
                    className='rounded-lg px-4 py-2 font-medium text-sm border-gray-300 text-gray-700 hover:bg-gray-50 transition-all duration-200'
                  >
                    <Trash2 className='h-4 w-4 mr-2' />
                    Discard
                  </Button>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
});
