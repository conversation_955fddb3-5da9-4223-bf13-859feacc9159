/**
 * Smart Refresh Manager - 2025 Pattern
 *
 * Handles incremental updates on page refresh.
 * Compares old vs new data, applies only changes.
 */

import { QueryClient } from '@tanstack/react-query';
import {
  cacheDB,
  type CachedUserProfile,
  type CachedAttachment,
} from './ticket-cache-db';
import { QueryKeys } from '@/lib/query-keys';
import type {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';

interface Changes {
  added: Ticket[];
  updated: Ticket[];
  deleted: string[];
  hasChanges: boolean;
}

export class SmartRefreshManager {
  constructor(private queryClient: QueryClient) {}

  /**
   * Handle page refresh with incremental sync
   */
  async handlePageRefresh(tenantId: string): Promise<void> {
    // 1. Load from IndexedDB (already filtered)
    const cachedTickets = await this.loadFromIndexedDB(tenantId);

    if (cachedTickets.length === 0) {
      // No cache - let normal flow handle it
      return;
    }

    // 2. Set cache immediately for instant display across all query variations
    // This ensures the UI shows data instantly regardless of which query key is used
    const queryKeysToUpdate = [
      QueryKeys.TICKETS.list(tenantId),
      QueryKeys.TICKETS.list(tenantId, { roleFilter: 'all' }),
      QueryKeys.TICKETS.list(tenantId, { roleFilter: 'assigned' }),
      QueryKeys.TICKETS.list(tenantId, { roleFilter: 'new' }),
    ];

    queryKeysToUpdate.forEach((queryKey) => {
      this.queryClient.setQueryData(queryKey, cachedTickets);
    });

    // 3. Background refresh with smart comparison
    this.silentRefresh(tenantId, cachedTickets);
  }

  /**
   * Load filtered tickets from IndexedDB
   */
  private async loadFromIndexedDB(tenantId: string): Promise<Ticket[]> {
    try {
      const twoDaysAgo = Date.now() - 48 * 60 * 60 * 1000;

      const tickets = await cacheDB.tickets
        .where('tenantId')
        .equals(tenantId)
        .filter(
          (ticket) =>
            ticket.status !== 'closed' ||
            new Date(ticket.updatedAt).getTime() > twoDaysAgo
        )
        .toArray();

      // Also restore user profiles to React Query cache
      const userProfiles = await cacheDB.userProfiles
        .where('tenantId')
        .equals(tenantId)
        .toArray();

      if (userProfiles.length > 0) {
        userProfiles.forEach((profile) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { _cachedAt, ...userProfile } = profile;
          this.queryClient.setQueryData(
            QueryKeys.USERS.detail(tenantId, profile.id),
            userProfile
          );
        });
        console.log(
          `🚀 Restored ${userProfiles.length} user profiles from IndexedDB`
        );
      }

      // Restore messages and attachments
      const cachedMessages = await cacheDB.messages
        .where('ticketId')
        .notEqual('')
        .toArray();

      // Group messages by ticket
      const messagesByTicket = new Map<string, TicketMessage[]>();
      cachedMessages.forEach((message) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _cachedAt, ...ticketMessage } = message;
        const ticketId = message.ticketId;

        if (!messagesByTicket.has(ticketId)) {
          messagesByTicket.set(ticketId, []);
        }
        messagesByTicket.get(ticketId)!.push(ticketMessage as TicketMessage);
      });

      // Restore to React Query cache
      messagesByTicket.forEach((messages, ticketId) => {
        this.queryClient.setQueryData(
          QueryKeys.TICKETS.messages(tenantId, ticketId),
          messages
        );
      });

      if (messagesByTicket.size > 0) {
        console.log(
          `🚀 Restored messages for ${messagesByTicket.size} tickets from IndexedDB`
        );
      }

      // Log attachment restoration
      const attachmentCount = await cacheDB.attachments
        .where('tenantId')
        .equals(tenantId)
        .count();

      if (attachmentCount > 0) {
        console.log(
          `🚀 ${attachmentCount} attachments available in IndexedDB cache`
        );
      }

      // Remove cache metadata
      return tickets.map((cachedTicket) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _cachedAt, _syncedAt, ...ticket } = cachedTicket;
        return ticket;
      });
    } catch (error) {
      console.warn('Failed to load from IndexedDB:', error);
      return [];
    }
  }

  /**
   * Refresh in background and apply only changes
   */
  private async silentRefresh(
    tenantId: string,
    oldData: Ticket[]
  ): Promise<void> {
    try {
      console.log('🔄 Starting silent background refresh...');

      // Fetch fresh data silently using the main query key
      const freshData = await this.queryClient.fetchQuery({
        queryKey: QueryKeys.TICKETS.list(tenantId),
        staleTime: 0, // Force fresh fetch
        gcTime: 0, // Don't cache this fetch
      });

      // Compare and find changes
      const changes = this.getIncrementalChanges(
        oldData,
        freshData as Ticket[]
      );

      if (changes.hasChanges) {
        console.log('✅ Applying incremental updates to UI...');

        // Apply only the changes
        this.applyIncrementalUpdate(tenantId, changes);

        // Update IndexedDB with fresh data
        await this.updateIndexedDB(tenantId, freshData as Ticket[]);

        // Trigger re-render for components using the data
        this.queryClient.invalidateQueries({
          queryKey: ['tickets', tenantId],
          exact: false,
          refetchType: 'none', // Don't refetch, just notify subscribers
        });
      } else {
        console.log('✅ No changes detected, cache is up to date');
      }
    } catch (error) {
      // Silent fail - user still sees cached data
      console.warn('⚠️ Silent refresh failed (user unaffected):', error);
    }
  }

  /**
   * Compare old vs new data to find changes
   * Enhanced to detect all types of changes including replies, status, priority, etc.
   */
  private getIncrementalChanges(oldData: Ticket[], newData: Ticket[]): Changes {
    const oldMap = new Map(oldData.map((t) => [t.id, t]));
    const newMap = new Map(newData.map((t) => [t.id, t]));

    const added: Ticket[] = [];
    const updated: Ticket[] = [];
    const deleted: string[] = [];

    // Find added and updated tickets
    newData.forEach((ticket) => {
      const oldTicket = oldMap.get(ticket.id);
      if (!oldTicket) {
        added.push(ticket);
      } else if (this.hasTicketChanged(oldTicket, ticket)) {
        updated.push(ticket);
      }
    });

    // Find deleted tickets
    oldData.forEach((ticket) => {
      if (!newMap.has(ticket.id)) {
        deleted.push(ticket.id);
      }
    });

    // Log changes for debugging
    if (added.length > 0 || updated.length > 0 || deleted.length > 0) {
      console.log('🔄 Smart refresh detected changes:', {
        added: added.length,
        updated: updated.length,
        deleted: deleted.length,
      });
    }

    return {
      added,
      updated,
      deleted,
      hasChanges: added.length + updated.length + deleted.length > 0,
    };
  }

  /**
   * Check if a ticket has any meaningful changes
   */
  private hasTicketChanged(oldTicket: Ticket, newTicket: Ticket): boolean {
    // Check all fields that could change
    return (
      oldTicket.updatedAt !== newTicket.updatedAt ||
      oldTicket.status !== newTicket.status ||
      oldTicket.priority !== newTicket.priority ||
      oldTicket.department !== newTicket.department ||
      oldTicket.assignedTo !== newTicket.assignedTo ||
      oldTicket.title !== newTicket.title ||
      oldTicket.description !== newTicket.description ||
      oldTicket.resolvedAt !== newTicket.resolvedAt ||
      oldTicket.closedAt !== newTicket.closedAt ||
      // Check message count if available
      (oldTicket.messages?.length || 0) !== (newTicket.messages?.length || 0) ||
      // Check attachment count
      (oldTicket.attachments?.length || 0) !==
        (newTicket.attachments?.length || 0)
    );
  }

  /**
   * Apply only the changes to React Query cache
   */
  private applyIncrementalUpdate(tenantId: string, changes: Changes): void {
    // Update all query variations to ensure UI consistency
    const queryKeysToUpdate = [
      QueryKeys.TICKETS.list(tenantId),
      QueryKeys.TICKETS.list(tenantId, { roleFilter: 'all' }),
      QueryKeys.TICKETS.list(tenantId, { roleFilter: 'assigned' }),
      QueryKeys.TICKETS.list(tenantId, { roleFilter: 'new' }),
      ['tickets', tenantId, 'list'],
      ['realtime-tickets', tenantId],
    ];

    queryKeysToUpdate.forEach((queryKey) => {
      this.queryClient.setQueriesData(
        { queryKey: queryKey, exact: false },
        (old: Ticket[] | undefined) => {
          if (!old || !Array.isArray(old)) return old;

          let result = [...old];

          // Remove deleted tickets
          if (changes.deleted.length > 0) {
            result = result.filter((t) => !changes.deleted.includes(t.id));
          }

          // Update modified tickets (preserve position)
          if (changes.updated.length > 0) {
            result = result.map((ticket) => {
              const updated = changes.updated.find((u) => u.id === ticket.id);
              return updated || ticket;
            });
          }

          // Add new tickets at beginning
          if (changes.added.length > 0) {
            result = [...changes.added, ...result];
          }

          // Force React to detect the change by creating new array reference
          return [...result];
        }
      );
    });

    // Also update individual ticket detail caches for updated tickets
    changes.updated.forEach((ticket) => {
      this.queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticket.id),
        ticket
      );
    });
  }

  /**
   * Update IndexedDB with fresh data
   */
  private async updateIndexedDB(
    tenantId: string,
    tickets: Ticket[]
  ): Promise<void> {
    try {
      const cachedTickets = tickets.map((ticket) => ({
        ...ticket,
        _cachedAt: Date.now(),
        _syncedAt: Date.now(),
      }));

      await cacheDB.tickets.bulkPut(cachedTickets);

      // Extract and cache user profiles from tickets
      await this.extractAndCacheUserProfiles(tickets, tenantId);

      // Extract and cache attachments from tickets
      await this.extractAndCacheAttachments(tickets, tenantId);
    } catch (error) {
      console.warn('Failed to update IndexedDB:', error);
    }
  }

  /**
   * Extract user profiles from tickets and cache them
   */
  private async extractAndCacheUserProfiles(
    tickets: Ticket[],
    tenantId: string
  ): Promise<void> {
    try {
      const userProfiles = new Map<string, CachedUserProfile>();

      tickets.forEach((ticket) => {
        // Extract creator user data
        if (ticket.userId && ticket.userName) {
          userProfiles.set(ticket.userId, {
            id: ticket.userId,
            name: ticket.userName,
            email: ticket.userEmail,
            ...(ticket.userAvatar && { avatarUrl: ticket.userAvatar }),
            ...(ticket.creatorClerkId && { clerkId: ticket.creatorClerkId }),
            role: 'user',
            tenantId,
            _cachedAt: Date.now(),
          });
        }

        // Extract assigned user data from metadata
        const metadata = ticket.metadata as Record<string, unknown>;
        if (ticket.assignedTo && metadata?.assignedUser) {
          const assignedUser = metadata.assignedUser as {
            name: string;
            email: string;
            avatar?: string;
            role?: string;
          };
          userProfiles.set(ticket.assignedTo, {
            id: ticket.assignedTo,
            name: assignedUser.name,
            email: assignedUser.email,
            ...(assignedUser.avatar && { avatarUrl: assignedUser.avatar }),
            ...(ticket.assignedToClerkId && {
              clerkId: ticket.assignedToClerkId,
            }),
            role: assignedUser.role || 'agent',
            tenantId,
            _cachedAt: Date.now(),
          });
        }
      });

      if (userProfiles.size > 0) {
        await cacheDB.userProfiles.bulkPut(Array.from(userProfiles.values()));
        console.log(
          `💾 Cached ${userProfiles.size} user profiles from tickets`
        );
      }
    } catch (error) {
      console.warn('Failed to cache user profiles:', error);
    }
  }

  /**
   * Extract attachments from tickets and cache them
   */
  private async extractAndCacheAttachments(
    tickets: Ticket[],
    tenantId: string
  ): Promise<void> {
    try {
      const attachments: CachedAttachment[] = [];

      tickets.forEach((ticket) => {
        // Extract attachments from ticket
        if (ticket.attachments && ticket.attachments.length > 0) {
          ticket.attachments.forEach((attachment) => {
            attachments.push({
              ...attachment,
              ticketId: ticket.id,
              tenantId,
              _cachedAt: Date.now(),
            });
          });
        }

        // Extract attachments from messages
        if (ticket.messages && ticket.messages.length > 0) {
          ticket.messages.forEach((message) => {
            if (message.attachments && message.attachments.length > 0) {
              message.attachments.forEach((attachment) => {
                // Check if already added from ticket attachments
                if (!attachments.find((a) => a.id === attachment.id)) {
                  attachments.push({
                    ...attachment,
                    messageId: message.id,
                    ticketId: ticket.id,
                    tenantId,
                    _cachedAt: Date.now(),
                  });
                }
              });
            }
          });
        }
      });

      if (attachments.length > 0) {
        await cacheDB.attachments.bulkPut(attachments);
        console.log(`💾 Cached ${attachments.length} attachments from tickets`);
      }

      // Also cache messages separately for quick access
      const messages: (TicketMessage & {
        ticketId: string;
        _cachedAt: number;
      })[] = [];
      tickets.forEach((ticket) => {
        if (ticket.messages && ticket.messages.length > 0) {
          ticket.messages.forEach((message) => {
            messages.push({
              ...message,
              ticketId: ticket.id,
              _cachedAt: Date.now(),
            });
          });
        }
      });

      if (messages.length > 0) {
        await cacheDB.messages.bulkPut(messages);
        console.log(`💾 Cached ${messages.length} messages from tickets`);
      }
    } catch (error) {
      console.warn('Failed to cache attachments:', error);
    }
  }
}
