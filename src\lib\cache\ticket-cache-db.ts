/**
 * Ticket Cache Database - 2025 Pattern
 *
 * Simple Dexie schema for persisting tickets across page refreshes.
 * Minimal approach - only what we need, no over-engineering.
 */

import <PERSON>ie, { Table } from 'dexie';
import type {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';

// Add cache metadata to existing types
export interface CachedTicket extends Ticket {
  _cachedAt: number;
  _syncedAt: number;
}

export interface CachedMessage extends TicketMessage {
  _cachedAt: number;
}

export interface CachedUserProfile {
  id: string;
  name: string;
  email: string;
  avatarUrl?: string;
  clerkId?: string;
  role?: string;
  tenantId: string;
  _cachedAt: number;
}

export interface CachedAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  uploadedAt: Date;
  ticketId?: string;
  messageId?: string;
  tenantId: string;
  _cachedAt: number;
}

// Simple database class
export class TicketCacheDB extends <PERSON><PERSON> {
  tickets!: Table<CachedTicket>;
  messages!: Table<CachedMessage>;
  userProfiles!: Table<CachedUserProfile>;
  attachments!: Table<CachedAttachment>;
  queryMetadata!: Table<{
    queryKey: string;
    lastFetched: number;
    lastModified: number;
  }>;

  constructor() {
    super('TicketingAppCache');

    // Define indexes - only what we query by
    this.version(3).stores({
      tickets: 'id, tenantId, status, updatedAt',
      messages: 'id, ticketId, createdAt',
      userProfiles: 'id, tenantId, [tenantId+id]',
      attachments:
        'id, tenantId, ticketId, messageId, [tenantId+ticketId], [tenantId+messageId]',
      queryMetadata: 'queryKey',
    });
  }
}

// Single instance
export const cacheDB = new TicketCacheDB();
