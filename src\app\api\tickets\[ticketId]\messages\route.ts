import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { z } from 'zod';
import { getStatusFromMessageReply } from '@/features/ticketing/utils/status-transitions';
import type { UserRole } from '@/features/ticketing/utils/status-transitions';
import type { TicketStatus } from '@/features/ticketing/models/ticket.schema';

// POST method for creating ticket messages (replies)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    // Await params before using
    const { ticketId } = await params;

    // Authentication check
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Note: Using service client for database operations, no additional token needed

    // Parse request body
    const body = await request.json();

    // Validate input
    const messageSchema = z.object({
      content: z.string().min(1, 'Message content is required'),
      tenant_id: z.string().uuid(),
      message_type: z
        .enum(['message', 'note', 'status_change'])
        .default('message'),
      is_internal: z.boolean().default(false),
      attachment_ids: z.array(z.string().uuid()).optional().default([]),
      is_resolve_action: z.boolean().optional().default(false),
    });

    const validatedData = messageSchema.parse(body);

    // Create service client for database operations
    const { createServiceSupabaseClient } = await import(
      '@/lib/supabase-server'
    );
    const serviceSupabase = createServiceSupabaseClient();

    // Verify ticket exists and user has access
    const { data: ticket, error: ticketAccessError } = await serviceSupabase
      .from('tickets')
      .select('id, tenant_id')
      .eq('id', ticketId)
      .eq('tenant_id', validatedData.tenant_id)
      .single();

    if (ticketAccessError || !ticket) {
      return NextResponse.json(
        { error: 'Ticket not found or access denied' },
        { status: 404 }
      );
    }

    // Get user information including role for status transition logic
    const { data: user, error: userError } = await serviceSupabase
      .from('users')
      .select('id, tenant_id, role, email')
      .eq('clerk_id', userId)
      .eq('tenant_id', validatedData.tenant_id)
      .single();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found or access denied' },
        { status: 403 }
      );
    }

    // Create the ticket message
    const { data: message, error: messageError } = await serviceSupabase
      .from('ticket_messages')
      .insert({
        tenant_id: validatedData.tenant_id,
        ticket_id: ticketId,
        author_id: user.id,
        content: validatedData.content,
        message_type: validatedData.message_type,
        is_internal: validatedData.is_internal,
      })
      .select('*')
      .single();

    if (messageError) {
      return NextResponse.json(
        { error: 'Failed to create message' },
        { status: 500 }
      );
    }

    // Link attachments to the message if any were provided
    if (
      validatedData.attachment_ids &&
      validatedData.attachment_ids.length > 0
    ) {
      const { data: updatedAttachments, error: attachmentError } =
        await serviceSupabase
          .from('attachments')
          .update({ message_id: message.id, ticket_id: null })
          .in('id', validatedData.attachment_ids)
          .eq('tenant_id', validatedData.tenant_id)
          .eq('uploaded_by', user.id)
          .select('id');

      if (attachmentError) {
        console.error('Failed to link attachments:', attachmentError);
        // Return a warning but don't fail the message creation
        return NextResponse.json(
          {
            message:
              'Reply created successfully but some attachments failed to link',
            data: message,
            warning:
              'Some attachments may not be properly linked to this message',
          },
          { status: 201 }
        );
      }

      // Verify all attachments were linked
      const linkedCount = updatedAttachments?.length || 0;
      if (linkedCount !== validatedData.attachment_ids.length) {
        console.warn(
          `Only ${linkedCount} of ${validatedData.attachment_ids.length} attachments were linked`
        );
        return NextResponse.json(
          {
            message:
              'Reply created successfully but not all attachments were linked',
            data: message,
            warning: `Only ${linkedCount} of ${validatedData.attachment_ids.length} attachments were linked`,
          },
          { status: 201 }
        );
      }
    }

    // Get current ticket to determine status transition
    const { data: currentTicket } = await serviceSupabase
      .from('tickets')
      .select('status, assigned_to')
      .eq('id', ticketId)
      .single();

    // Determine automatic status transition based on user role, current status, and resolve action
    let newStatus = currentTicket?.status;

    if (currentTicket) {
      const statusFromReply = getStatusFromMessageReply(
        currentTicket.status as TicketStatus,
        user.role as UserRole,
        validatedData.is_resolve_action
      );

      if (statusFromReply) {
        newStatus = statusFromReply;
      }
    }

    // Update ticket with new status and timestamp
    const updateData: Record<string, unknown> = {
      updated_at: new Date().toISOString(),
    };

    if (newStatus && newStatus !== currentTicket?.status) {
      updateData.status = newStatus;

      // Add resolved_at timestamp if transitioning to resolved
      if (newStatus === 'resolved') {
        updateData.resolved_at = new Date().toISOString();
      }
    }

    await serviceSupabase.from('tickets').update(updateData).eq('id', ticketId);

    // Fetch complete message with author details
    const { data: author } = await serviceSupabase
      .from('users')
      .select('id, first_name, last_name, email, avatar_url, role')
      .eq('id', user.id)
      .single();

    const messageWithAuthor = {
      ...message,
      author: author || {
        id: user.id,
        first_name: 'Unknown',
        last_name: 'User',
        email: user.email || '<EMAIL>',
        avatar_url: null,
        role: user.role,
      },
      attachments: [], // Will be populated by real-time or next fetch
    };

    return NextResponse.json(
      {
        message: messageWithAuthor,
        data: messageWithAuthor, // Keep for backward compatibility
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.issues,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET method for fetching ticket messages
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    // Await params before using
    const { ticketId } = await params;

    // Authentication check
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get tenant_id from query parameters
    const { searchParams } = new URL(request.url);
    const tenantParam = searchParams.get('tenant_id');

    if (!tenantParam) {
      return NextResponse.json(
        { error: 'tenant_id parameter is required' },
        { status: 400 }
      );
    }

    // Create service client
    const { createServiceSupabaseClient } = await import(
      '@/lib/supabase-server'
    );
    const serviceSupabase = createServiceSupabaseClient();

    // Resolve tenant subdomain to UUID if needed
    let tenantUuid = tenantParam;
    if (
      !tenantParam.match(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
      )
    ) {
      const { data: tenantData, error: tenantError } = await serviceSupabase
        .from('tenants')
        .select('id')
        .eq('subdomain', tenantParam)
        .single();

      if (tenantError || !tenantData) {
        return NextResponse.json({ error: 'Invalid tenant' }, { status: 400 });
      }
      tenantUuid = tenantData.id;
    }

    // Verify user has access to this tenant
    const { data: user, error: userError } = await serviceSupabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .eq('tenant_id', tenantUuid)
      .single();

    if (userError || !user) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Fetch ticket messages first
    const { data: messages, error: messagesError } = await serviceSupabase
      .from('ticket_messages')
      .select('*')
      .eq('ticket_id', ticketId)
      .eq('tenant_id', tenantUuid)
      .order('created_at', { ascending: true });

    if (messagesError) {
      console.error('Error fetching ticket messages:', messagesError);
      return NextResponse.json(
        { error: 'Failed to fetch messages' },
        { status: 500 }
      );
    }

    // Fetch authors and attachments separately for better reliability
    const messagesWithDetails = await Promise.all(
      (messages || []).map(async (message) => {
        // Fetch author information
        const { data: author } = await serviceSupabase
          .from('users')
          .select('id, first_name, last_name, email, avatar_url, role')
          .eq('id', message.author_id)
          .single();

        // Fetch attachments
        const { data: attachments } = await serviceSupabase
          .from('attachments')
          .select(
            'id, file_name, file_type, file_size, storage_path, created_at'
          )
          .eq('message_id', message.id);

        return {
          ...message,
          author: author || {
            id: message.author_id,
            first_name: 'Unknown',
            last_name: 'User',
            email: '<EMAIL>',
            avatar_url: null,
            role: 'user',
          },
          attachments: attachments || [],
        };
      })
    );

    return NextResponse.json({
      messages: messagesWithDetails || [],
    });
  } catch (error) {
    console.error('Error in ticket messages GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
