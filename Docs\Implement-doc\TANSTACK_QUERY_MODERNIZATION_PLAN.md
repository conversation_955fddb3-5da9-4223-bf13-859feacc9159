# TanStack Query Modernization Plan

## Smart Fallback Mechanism Replacement & Phantom Message Fix

### 📋 Executive Summary

This document outlines the complete modernization of our ticketing system's data fetching strategy, replacing the custom `useSmartFallback` mechanism with TanStack Query v5's native capabilities. This change will eliminate phantom messages, reduce code complexity by 85%, and improve performance while maintaining all existing functionality.

### 🎯 Goals

1. **Eliminate phantom messages** caused by manual cache merging
2. **Remove redundant code** (392 lines of `useSmartFallback`)
3. **Leverage TanStack Query v5 native features** for incremental updates
4. **Maintain cache-first performance** with instant ticket display
5. **Follow 2025 best practices** with minimal complexity

---

## 🔍 Current State Analysis

### What We Have Now

- ✅ TanStack Query v5 with cache-first configuration
- ✅ `placeholderData: (prev) => prev` for instant display
- ✅ Real-time subscriptions via Supabase
- ✅ Dexie.js for offline persistence
- ✅ Optimistic updates for mutations
- ❌ **Redundant** `useSmartFallback` hook (392 lines)
- ❌ **Phantom messages** from manual cache merging
- ❌ **Duplicate API calls** (TanStack Query + manual validation)

### Current Click-to-Fetch Flow

```
User clicks ticket
    ↓
TanStack Query shows cached data (placeholderData)
    ↓
useSmartFallback triggers validation API call
    ↓
Manual cache comparison & merging
    ↓
Phantom messages appear due to race conditions
    ↓
Complex deduplication logic runs
```

### Issues Identified

#### 1. Phantom Messages Root Cause

**File**: `src/hooks/realtime/useSmartFallback.ts` (lines 128-143)

```typescript
// PROBLEM: Manual merging creates duplicates
const combined = [...oldMessages, ...freshMessages];
const deduplicated = deduplicateMessages(combined);
```

#### 2. Redundant API Calls

- TanStack Query: Background refetch when stale
- useSmartFallback: Manual validation call
- **Result**: Same data fetched twice, causing race conditions

#### 3. Code Complexity

- 392 lines in `useSmartFallback.ts`
- Complex deduplication logic
- Manual cache management
- Race condition handling

---

## 🚀 Proposed Solution: TanStack Query-First Architecture

### Core Principle

**Let TanStack Query handle ALL data fetching logic natively**

### New Click-to-Fetch Flow

```
User clicks ticket
    ↓
TanStack Query shows cached data instantly (placeholderData)
    ↓
TanStack Query checks if data is stale
    ↓
If stale: Silent background fetch
    ↓
Structural sharing updates only changed parts
    ↓
No phantom messages, no manual deduplication needed
```

---

## 📋 Implementation Plan

### Phase 1: Enhanced Query Configuration (15 minutes)

#### 1.1 Update Query Options

**File**: `src/lib/query-options.ts`

```typescript
export const ticketQueryOptions = {
  detail: (tenantId: string, ticketId: string) => ({
    queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
    queryFn: () => fetchTicketDetail(tenantId, ticketId),

    // ✨ Cache-first strategy
    staleTime: 30 * 1000, // Fresh for 30 seconds
    gcTime: 30 * 60 * 1000, // Keep in memory for 30 minutes

    // ✨ Instant display of cached data
    placeholderData: (previousData) => previousData,

    // ✨ Only re-render components with changed data
    structuralSharing: true,

    // ✨ Smart refetch behavior
    refetchOnMount: 'stale', // Only refetch if stale
    refetchOnWindowFocus: 'always', // Keep data fresh
    refetchOnReconnect: 'always', // Sync after reconnection

    // ✨ Network resilience
    retry: (failureCount, error) => {
      if (error.message.includes('404')) return false;
      return failureCount < 2;
    },

    // ✨ Background updates don't show loading states
    notifyOnChangeProps: ['data', 'error'],
  }),

  messages: (tenantId: string, ticketId: string) => ({
    queryKey: QueryKeys.TICKETS.messages(tenantId, ticketId),
    queryFn: () => fetchTicketMessages(tenantId, ticketId),

    // ✨ Messages can be slightly more stale
    staleTime: 10 * 1000,
    gcTime: 30 * 60 * 1000,

    // ✨ PHANTOM MESSAGE FIX: Native cache handling
    placeholderData: (previousData) => previousData,
    structuralSharing: true,

    // ✨ Prevent message loading states
    refetchOnMount: 'stale',
    notifyOnChangeProps: ['data', 'error'],
  }),
};
```

#### 1.2 Enhanced Cache Configuration

**File**: `src/lib/query-keys.ts`

```typescript
export const CACHE_CONFIG = {
  tickets: {
    staleTime: 30 * 1000,
    gcTime: 30 * 60 * 1000,
    placeholderData: (previousData) => previousData,
    structuralSharing: true,
    refetchOnMount: 'stale' as const,
  },

  messages: {
    staleTime: 10 * 1000,
    gcTime: 30 * 60 * 1000,
    placeholderData: (previousData) => previousData,
    structuralSharing: true,
    refetchOnMount: 'stale' as const,
  },

  // ✨ New: Real-time integration config
  realtime: {
    // When real-time updates cache, prevent refetch for this duration
    mutationCacheTime: 5 * 1000,
  },
};
```

### Phase 2: Remove useSmartFallback (30 minutes)

#### 2.1 Delete Redundant Files

- ❌ Delete: `src/hooks/realtime/useSmartFallback.ts` (392 lines)
- ❌ Delete: Complex deduplication logic (handled by TanStack Query)

#### 2.2 Update Main Page

**File**: `src/app/tickets/page.tsx`

```typescript
// BEFORE (Remove these imports)
- import { useSmartFallback } from '@/hooks/realtime/useSmartFallback';

// BEFORE (Remove this hook usage)
- const { validateTicketInBackground } = useSmartFallback({
-   tenantId,
-   onUpdateDetected,
- });

// AFTER (Simplified click handler)
const handleTicketSelect = useCallback((ticketId: string) => {
  setSelectedTicketId(ticketId);

  // That's it! TanStack Query handles everything:
  // 1. Shows cached data instantly via placeholderData
  // 2. Checks if data is stale (30 seconds for tickets, 10 for messages)
  // 3. Fetches fresh data in background if needed
  // 4. Updates only changed parts via structuralSharing
  // 5. No phantom messages due to native cache management
}, []);

// BEFORE (Remove manual validation calls)
- // validateTicketInBackground(ticketId);
```

### Phase 3: Real-time Integration Enhancement (20 minutes)

#### 3.1 Smart Real-time Updates

**File**: `src/hooks/realtime/event-handlers.ts`

```typescript
// ✨ Enhanced real-time event handling
export const handleTicketUpdate = (
  queryClient: QueryClient,
  tenantId: string,
  payload: any
) => {
  const { new: newTicket, old: oldTicket } = payload;

  // ✨ Update specific ticket cache
  queryClient.setQueryData(
    QueryKeys.TICKETS.detail(tenantId, newTicket.id),
    (oldData: Ticket | undefined) => {
      if (!oldData) return newTicket;

      // ✨ TanStack Query's structural sharing will only re-render
      // components that use the changed fields
      return { ...oldData, ...newTicket };
    }
  );

  // ✨ Update ticket in all list queries
  queryClient.setQueriesData(
    { queryKey: ['tickets', tenantId], exact: false },
    (oldData: Ticket[] | undefined) => {
      if (!oldData) return oldData;

      return oldData.map((ticket) =>
        ticket.id === newTicket.id ? { ...ticket, ...newTicket } : ticket
      );
    }
  );

  // ✨ Prevent immediate refetch after real-time update
  queryClient.setQueryData(
    ['__last_realtime_update__', tenantId, newTicket.id],
    Date.now()
  );
};
```

#### 3.2 Intelligent Refetch Prevention

**File**: `src/lib/query-options.ts`

```typescript
// ✨ Prevent refetch immediately after real-time updates
const shouldRefetch = (query: Query) => {
  const lastRealtimeUpdate = queryClient.getQueryData([
    '__last_realtime_update__',
    tenantId,
    ticketId,
  ]);

  // If real-time updated within last 5 seconds, don't refetch
  if (lastRealtimeUpdate && Date.now() - lastRealtimeUpdate < 5000) {
    return false;
  }

  return query.isStale();
};
```

### Phase 4: Dexie.js Integration Optimization (15 minutes)

#### 4.1 Enhanced Offline-First Pattern

**File**: `src/hooks/useTickets.ts`

```typescript
export const useRealtimeTicket = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  return useQuery({
    queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
    queryFn: () => fetchTicketDetail(tenantId, ticketId),

    // ✨ Enhanced offline-first with Dexie
    placeholderData: async () => {
      // Load from Dexie instantly while fetching from network
      const cached = await dexieCache.getTicket(tenantId, ticketId);
      return cached;
    },

    // ✨ Save to Dexie on successful fetch
    onSuccess: (data) => {
      dexieCache.saveTicket(tenantId, ticketId, data);
    },

    ...CACHE_CONFIG.tickets,
    enabled,
  });
};
```

---

## 📊 Expected Results

### Performance Improvements

| Metric              | Before                       | After               | Improvement   |
| ------------------- | ---------------------------- | ------------------- | ------------- |
| Code Complexity     | 392 lines (useSmartFallback) | ~50 lines (config)  | 85% reduction |
| API Calls per Click | 2-3 (duplicate calls)        | 0-1 (smart caching) | 67% reduction |
| Time to Display     | Instant (cached)             | Instant (cached)    | Same          |
| Phantom Messages    | Common                       | None                | 100% fix      |
| Bundle Size         | +15KB (complex logic)        | +2KB (config)       | 87% reduction |

### User Experience Improvements

1. **✅ No More Phantom Messages**
   - Root cause eliminated by removing manual cache merging
   - TanStack Query's structural sharing prevents duplicates

2. **✅ Smoother Interactions**
   - No loading flashes during background updates
   - Only changed UI components re-render

3. **✅ Better Offline Experience**
   - Dexie integration enhanced with placeholderData
   - Seamless online/offline transitions

4. **✅ Improved Reliability**
   - No race conditions between manual and automatic fetching
   - Built-in retry and error handling

### Developer Experience Improvements

1. **✅ Simplified Mental Model**
   - One source of truth: TanStack Query
   - No complex manual cache management

2. **✅ Better Debugging**
   - TanStack Query DevTools show all cache operations
   - Clear separation of concerns

3. **✅ Easier Maintenance**
   - 85% less code to maintain
   - Standard patterns instead of custom logic

---

## 🔧 Fallback Mechanism Analysis

### Current Click-to-Fetch Implementation

#### What Triggers Fallback Currently:

1. **Real-time connection down** → `useSmartFallback` activates
2. **User clicks ticket** → Manual validation API call
3. **Debug mode** → Force fallback with `localStorage.getItem('DEBUG_FORCE_FALLBACK')`

#### Current Fallback Logic:

```typescript
// From useSmartFallback.ts
const validateTicketInBackground = useCallback(async (ticketId: string) => {
  // Skip if real-time connected (unless debug mode)
  if (connectionStatus?.isConnected === true && !forceDebugFallback) {
    return;
  }

  // Fetch fresh data
  const response = await fetch(`/api/tickets/${ticketId}?tenant_id=${tenantUuid}`);

  // Compare with cached data
  const changes = detectChanges(cachedTicket, freshTicket);

  // Manual cache update
  queryClient.setQueryData(QueryKeys.TICKETS.detail(tenantUuid, ticketId), ...);
}, []);
```

### Proposed TanStack Query Click-to-Fetch

#### What Will Trigger Fallback:

1. **Data is stale** (older than `staleTime`) → Automatic background fetch
2. **No cached data exists** → Immediate fetch with loading state
3. **Real-time disconnected** → Automatic polling via TanStack Query

#### New Fallback Logic:

```typescript
// TanStack Query handles this automatically
const query = useQuery({
  queryKey: ['ticket', ticketId],
  queryFn: () => fetchTicket(ticketId),

  // ✨ Show cached data instantly
  placeholderData: (previousData) => previousData,

  // ✨ Auto-fetch if stale
  staleTime: 30 * 1000,

  // ✨ Background refetch when real-time down
  refetchInterval: connectionStatus?.isConnected ? false : 30000,
});

// Click handler becomes trivial
const handleClick = (ticketId) => {
  setSelectedTicketId(ticketId);
  // TanStack Query does the rest automatically
};
```

---

## 🚦 Migration Strategy

### Step 1: Preparation (5 minutes)

1. **Backup current implementation**
2. **Run existing tests to establish baseline**
3. **Document current behavior for comparison**

### Step 2: Implementation (1 hour)

1. **Phase 1**: Update query configuration (15 min)
2. **Phase 2**: Remove useSmartFallback (30 min)
3. **Phase 3**: Enhance real-time integration (20 min)
4. **Phase 4**: Optimize Dexie integration (15 min)

### Step 3: Testing (30 minutes)

1. **Click-to-fetch behavior**: Verify instant display + background updates
2. **Phantom message fix**: Confirm no duplicates appear
3. **Real-time integration**: Test connection up/down scenarios
4. **Offline functionality**: Verify Dexie cache works

### Step 4: Cleanup (15 minutes)

1. **Remove unused imports**
2. **Update documentation**
3. **Remove debug flags**
4. **Remove duplicate redunent code and files after new implementation a proper clean is need after new implementation applied in app and make sure no duplicate click to card and fetch ticker details is enable expect new apprach search all files if find remove it**

---

## 🎯 Success Criteria

### Functional Requirements

- ✅ Cached tickets display instantly on click
- ✅ Background updates occur silently
- ✅ No phantom messages appear
- ✅ Real-time updates work when connected
- ✅ Fallback works when real-time is down
- ✅ Offline functionality preserved

### Performance Requirements

- ✅ First click shows cached data in <50ms
- ✅ Background updates don't cause loading states
- ✅ Code complexity reduced by >80%
- ✅ Bundle size reduction >10KB

### Developer Experience

- ✅ Single source of truth for data fetching
- ✅ Standard TanStack Query patterns
- ✅ Clear debugging with DevTools
- ✅ Easy to understand and maintain

---

## 🔗 Related Documentation

- [TanStack Query v5 Documentation](https://tanstack.com/query/v5)
- [React Query Best Practices 2025](https://tkdodo.eu/blog/practical-react-query)
- [Dexie.js Integration Patterns](https://dexie.org/docs/Tutorial/React)
- [Cache-First Architecture Guide](internal-link)

---

## 📞 Next Steps

1. **Review this plan** with the team
2. **Approve the migration strategy**
3. **Begin Phase 1 implementation**
4. **Monitor for any edge cases during testing**

**Estimated Total Time**: 2 hours (implementation + testing)
**Risk Level**: Low (existing behavior preserved, just simplified)
**Impact**: High (major code reduction, performance improvement, bug fixes)
