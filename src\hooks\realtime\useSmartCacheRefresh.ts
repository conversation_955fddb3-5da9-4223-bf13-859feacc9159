/**
 * Smart Cache Refresh Hook - 2025 Pattern
 *
 * Integrates <PERSON>ie persistence with incremental refresh.
 * Shows cached data instantly, syncs changes in background.
 */

'use client';

import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { SmartRefreshManager } from '@/lib/cache/smart-refresh-manager';
import { useSmartCache } from '@/hooks/useSmartCache';

export function useSmartCacheRefresh(tenantId: string) {
  const queryClient = useQueryClient();
  const refreshManagerRef = useRef<SmartRefreshManager | null>(null);
  const hasRefreshedRef = useRef(false);

  // Initialize Dexie cache persistence
  useSmartCache();

  useEffect(() => {
    if (!tenantId) return;

    // Create refresh manager if not exists
    if (!refreshManagerRef.current) {
      refreshManagerRef.current = new SmartRefreshManager(queryClient);
    }

    // CACHE-FIRST: Handle initial page load - load from IndexedDB instantly
    // This ensures we show cached data immediately without any loading state
    if (!hasRefreshedRef.current) {
      refreshManagerRef.current.handlePageRefresh(tenantId);
      hasRefreshedRef.current = true;
    }

    // Smart cache refresh on page focus (keep existing functionality)
    const handleFocus = () => {
      // CRITICAL FIX: Defer focus refresh to avoid interfering with user clicks
      // This prevents race conditions when user clicks immediately after window focus
      setTimeout(() => {
        // Only refresh stale data that isn't currently being fetched
        queryClient.invalidateQueries({
          predicate: (query) => {
            const dataAge = Date.now() - query.state.dataUpdatedAt;
            // Don't invalidate if query is currently fetching (user interaction in progress)
            if (query.state.fetchStatus === 'fetching') return false;
            return dataAge > 30 * 1000; // Only refresh if older than 30 seconds
          },
        });
      }, 100); // Small delay to allow user clicks to complete first
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [tenantId, queryClient]);
}
