/**
 * Real-time Connection Manager - 2025 Minimal Pattern
 *
 * Manages Supabase real-time connection lifecycle with singleton pattern.
 * One connection per tenant for efficiency.
 */

import { SupabaseClient } from '@supabase/supabase-js';
import { RealtimeChannel } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

export class RealtimeConnectionManager {
  private static instances = new Map<string, RealtimeConnectionManager>();
  private channel: RealtimeChannel | null = null;
  private subscribers = new Set<string>();
  private isConnected = false;
  private connectionListeners = new Set<(connected: boolean) => void>();

  private constructor(
    private tenantId: string,
    private supabase: SupabaseClient<Database>
  ) {}

  static getInstance(
    tenantId: string,
    supabase: SupabaseClient<Database>
  ): RealtimeConnectionManager {
    if (!this.instances.has(tenantId)) {
      this.instances.set(
        tenantId,
        new RealtimeConnectionManager(tenantId, supabase)
      );
    }
    return this.instances.get(tenantId)!;
  }

  addSubscriber(subscriberId: string): RealtimeChannel | null {
    this.subscribers.add(subscriberId);

    if (this.subscribers.size === 1 && !this.channel) {
      this.createChannel();
    }

    return this.channel;
  }

  removeSubscriber(subscriberId: string): void {
    this.subscribers.delete(subscriberId);

    if (this.subscribers.size === 0 && this.channel) {
      this.destroyChannel();
    }
  }

  private createChannel(): void {
    const channelName = `realtime-${this.tenantId}`;

    this.channel = this.supabase
      .channel(channelName)
      .on('system', {}, (payload) => {
        if (payload.type === 'connected') {
          this.setConnectionStatus(true);
        }
      });
  }

  private destroyChannel(): void {
    if (this.channel) {
      this.supabase.removeChannel(this.channel);
      this.channel = null;
      this.setConnectionStatus(false);
    }
  }

  private setConnectionStatus(connected: boolean): void {
    this.isConnected = connected;
    this.connectionListeners.forEach((listener) => listener(connected));
  }

  onConnectionChange(listener: (connected: boolean) => void): () => void {
    this.connectionListeners.add(listener);
    return () => this.connectionListeners.delete(listener);
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  static cleanup(tenantId: string): void {
    const instance = this.instances.get(tenantId);
    if (instance?.subscribers.size === 0) {
      instance.destroyChannel();
      this.instances.delete(tenantId);
    }
  }
}
