/**
 * UI State Store - 2025 Optimized
 *
 * Zustand store focused purely on UI state management
 * Server state is handled by React Query
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// UI-only state interface
interface UIState {
  // Ticket UI state
  selectedTicketId: string | null;
  ticketSidebarCollapsed: boolean;
  ticketFilters: {
    status: string[];
    priority: string[];
    assignedTo: string | null;
    search: string;
  };

  // Message UI state
  collapsedMessages: Record<string, boolean>; // messageId -> collapsed
  replyingTo: string | null; // messageId

  // General UI state
  sidebarOpen: boolean;
  theme: 'light' | 'dark' | 'system';
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: number;
  }>;

  // Loading states (for UI feedback only - server state loading handled by React Query)
  loadingStates: Record<string, boolean>;
}

interface UIActions {
  // Ticket actions
  setSelectedTicketId: (ticketId: string | null) => void;
  setTicketSidebarCollapsed: (collapsed: boolean) => void;
  setTicketFilters: (filters: Partial<UIState['ticketFilters']>) => void;
  resetTicketFilters: () => void;

  // Message actions
  setMessageCollapsed: (messageId: string, collapsed: boolean) => void;
  setReplyingTo: (messageId: string | null) => void;

  // General UI actions
  setSidebarOpen: (open: boolean) => void;
  setTheme: (theme: UIState['theme']) => void;

  // Notification actions
  addNotification: (
    notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>
  ) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;

  // Loading state actions
  setLoading: (key: string, loading: boolean) => void;
  clearLoading: (key: string) => void;

  // Reset actions
  resetUIState: () => void;
}

const initialState: UIState = {
  selectedTicketId: null,
  ticketSidebarCollapsed: false,
  ticketFilters: {
    status: [],
    priority: [],
    assignedTo: null,
    search: '',
  },
  collapsedMessages: {},
  replyingTo: null,
  sidebarOpen: false,
  theme: 'system',
  notifications: [],
  loadingStates: {},
};

export const useUIStore = create<UIState & UIActions>()(
  devtools(
    persist(
      (set) => ({
        ...initialState,

        // Ticket actions
        setSelectedTicketId: (ticketId) =>
          set({ selectedTicketId: ticketId }, false, 'setSelectedTicketId'),

        setTicketSidebarCollapsed: (collapsed) =>
          set(
            { ticketSidebarCollapsed: collapsed },
            false,
            'setTicketSidebarCollapsed'
          ),

        setTicketFilters: (filters) =>
          set(
            (state) => ({
              ticketFilters: { ...state.ticketFilters, ...filters },
            }),
            false,
            'setTicketFilters'
          ),

        resetTicketFilters: () =>
          set(
            { ticketFilters: initialState.ticketFilters },
            false,
            'resetTicketFilters'
          ),

        // Message actions
        setMessageCollapsed: (messageId, collapsed) =>
          set(
            (state) => ({
              collapsedMessages: {
                ...state.collapsedMessages,
                [messageId]: collapsed,
              },
            }),
            false,
            'setMessageCollapsed'
          ),

        setReplyingTo: (messageId) =>
          set({ replyingTo: messageId }, false, 'setReplyingTo'),

        // General UI actions
        setSidebarOpen: (open) =>
          set({ sidebarOpen: open }, false, 'setSidebarOpen'),

        setTheme: (theme) => set({ theme }, false, 'setTheme'),

        // Notification actions
        addNotification: (notification) =>
          set(
            (state) => ({
              notifications: [
                ...state.notifications,
                {
                  ...notification,
                  id: `notification-${Date.now()}-${Math.random()}`,
                  timestamp: Date.now(),
                },
              ],
            }),
            false,
            'addNotification'
          ),

        removeNotification: (id) =>
          set(
            (state) => ({
              notifications: state.notifications.filter((n) => n.id !== id),
            }),
            false,
            'removeNotification'
          ),

        clearNotifications: () =>
          set({ notifications: [] }, false, 'clearNotifications'),

        // Loading state actions
        setLoading: (key, loading) =>
          set(
            (state) => ({
              loadingStates: {
                ...state.loadingStates,
                [key]: loading,
              },
            }),
            false,
            'setLoading'
          ),

        clearLoading: (key) =>
          set(
            (state) => {
              const { [key]: _removed, ...rest } = state.loadingStates; // eslint-disable-line @typescript-eslint/no-unused-vars
              return { loadingStates: rest };
            },
            false,
            'clearLoading'
          ),

        // Reset actions
        resetUIState: () => set(initialState, false, 'resetUIState'),
      }),
      {
        name: 'ui-store',
        partialize: (state) => ({
          // Only persist UI preferences, not user-specific state
          ticketSidebarCollapsed: state.ticketSidebarCollapsed,
          theme: state.theme,
          sidebarOpen: state.sidebarOpen,
          // Don't persist selectedTicketId or ticketFilters - they should reset on logout/login
        }),
      }
    ),
    {
      name: 'ui-store',
    }
  )
);

// Selectors for optimized re-renders
export const useUISelectors = {
  // Ticket selectors
  useSelectedTicketId: () => useUIStore((state) => state.selectedTicketId),
  useTicketSidebarCollapsed: () =>
    useUIStore((state) => state.ticketSidebarCollapsed),
  useTicketFilters: () => useUIStore((state) => state.ticketFilters),

  // Message selectors
  useCollapsedMessages: () => useUIStore((state) => state.collapsedMessages),
  useReplyingTo: () => useUIStore((state) => state.replyingTo),
  useMessageCollapsed: (messageId: string) =>
    useUIStore((state) => state.collapsedMessages[messageId] || false),

  // General UI selectors
  useSidebarOpen: () => useUIStore((state) => state.sidebarOpen),
  useTheme: () => useUIStore((state) => state.theme),
  useNotifications: () => useUIStore((state) => state.notifications),

  // Loading selectors
  useLoadingStates: () => useUIStore((state) => state.loadingStates),
  useIsLoading: (key: string) =>
    useUIStore((state) => state.loadingStates[key] || false),
};

// Individual action selectors to prevent re-render issues
export const useUIActions = {
  useSetSelectedTicketId: () =>
    useUIStore((state) => state.setSelectedTicketId),
  useSetTicketSidebarCollapsed: () =>
    useUIStore((state) => state.setTicketSidebarCollapsed),
  useSetTicketFilters: () => useUIStore((state) => state.setTicketFilters),
  useResetTicketFilters: () => useUIStore((state) => state.resetTicketFilters),
  useSetMessageCollapsed: () =>
    useUIStore((state) => state.setMessageCollapsed),
  useSetReplyingTo: () => useUIStore((state) => state.setReplyingTo),
  useSetSidebarOpen: () => useUIStore((state) => state.setSidebarOpen),
  useSetTheme: () => useUIStore((state) => state.setTheme),
  useAddNotification: () => useUIStore((state) => state.addNotification),
  useRemoveNotification: () => useUIStore((state) => state.removeNotification),
  useClearNotifications: () => useUIStore((state) => state.clearNotifications),
  useSetLoading: () => useUIStore((state) => state.setLoading),
  useClearLoading: () => useUIStore((state) => state.clearLoading),
  useResetUIState: () => useUIStore((state) => state.resetUIState),
};
