import { useCallback } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/features/shared/hooks/useAuth';
import type { Database } from '@/types/supabase';

export type TenantDepartment =
  Database['public']['Tables']['tenant_departments']['Row'];

/**
 * Uses the existing useQuery pattern for consistency
 */
export function useDepartments(tenantId: string | null, enabled = true) {
  const { isAdmin, isSuperAdmin } = useAuth();

  // Only admin/super_admin can access department management
  const canManageDepartments = isAdmin || isSuperAdmin;

  return useQuery<TenantDepartment[]>({
    queryKey: ['departments', tenantId || ''],
    queryFn: async () => {
      if (!tenantId) {
        throw new Error('Tenant ID is required');
      }

      console.log('🔍 Fetching departments for tenant:', tenantId);

      // Call the API endpoint instead of querying the database directly
      const response = await fetch('/api/departments');

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Error fetching departments:', errorData);
        throw new Error(errorData.error || 'Failed to fetch departments');
      }

      const data = await response.json();
      console.log('✅ Fetched departments:', data?.length || 0);
      return (data || []) as TenantDepartment[];
    },
    enabled: enabled && !!tenantId && canManageDepartments,
    staleTime: 1000 * 60 * 5, // 5 minutes - departments don't change frequently
    gcTime: 1000 * 60 * 30, // 30 minutes
  });
}

/**
 * Hook for fetching active departments only (for dropdowns, etc.)
 * This can be used by non-admin users for ticket creation
 *
 * Real-time updates: When a department's is_active status changes,
 * the real-time subscription will automatically update the cache
 * and this hook will re-filter to show only active departments.
 */
export function useActiveDepartments(tenantId: string | null, enabled = true) {
  const query = useQuery<TenantDepartment[]>({
    queryKey: ['departments', 'active', tenantId || ''],
    queryFn: async () => {
      if (!tenantId) {
        throw new Error('Tenant ID is required');
      }

      console.log('🔍 Fetching active departments for tenant:', tenantId);

      // Call the API endpoint and filter for active departments
      const response = await fetch('/api/departments');

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Error fetching active departments:', errorData);
        throw new Error(errorData.error || 'Failed to fetch departments');
      }

      const allDepartments = await response.json();
      const activeDepartments = allDepartments.filter(
        (dept: TenantDepartment) => dept.is_active
      );

      console.log(
        '✅ Fetched active departments:',
        activeDepartments?.length || 0,
        'out of',
        allDepartments?.length || 0,
        'total departments'
      );
      return (activeDepartments || []) as TenantDepartment[];
    },
    enabled: enabled && !!tenantId,
    staleTime: 1000 * 60 * 30, // 30 minutes - departments rarely change
    gcTime: 1000 * 60 * 60, // 60 minutes
  });

  // Add smart background validation method with throttling
  const validateInBackground = useCallback(async () => {
    if (!tenantId || query.isFetching) return;

    // Smart caching: Only refetch if data is stale (older than 30 seconds)
    const dataAge = Date.now() - (query.dataUpdatedAt || 0);
    const isStale = dataAge > 30 * 1000; // 30 seconds

    if (!isStale) {
      console.log('🔄 Smart validation: Data is fresh, skipping refetch');
      return;
    }

    console.log('🔄 Smart background validation triggered for departments');
    await query.refetch();
  }, [tenantId, query]);

  return {
    ...query,
    validateInBackground,
  };
}
