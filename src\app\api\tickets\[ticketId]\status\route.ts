import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { z } from 'zod';

const schema = z.object({
  tenant_id: z.string(),
  new_status: z.enum(['new', 'open', 'pending', 'resolved', 'closed']),
});

const transitions: Record<string, string[]> = {
  new: ['open'],
  open: ['pending', 'resolved', 'closed'],
  pending: ['open', 'resolved', 'closed'],
  resolved: ['open', 'closed'],
  closed: ['open'],
};

const permissions: Record<string, string[]> = {
  agent: ['open', 'pending', 'resolved', 'closed'],
  admin: ['open', 'pending', 'resolved', 'closed'],
  super_admin: ['open', 'pending', 'resolved', 'closed'],
};

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    const { ticketId } = await params;
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { tenant_id, new_status } = schema.parse(body);

    const supabase = createServiceSupabaseClient();

    // Get tenant UUID
    const { data: tenant } = await supabase
      .from('tenants')
      .select('id')
      .eq('subdomain', tenant_id)
      .single();

    if (!tenant) {
      return NextResponse.json({ error: 'Tenant not found' }, { status: 404 });
    }

    // Get user data
    const { data: user } = await supabase
      .from('users')
      .select('id, role')
      .eq('clerk_id', userId)
      .eq('tenant_id', tenant.id)
      .single();

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get current ticket
    const { data: ticket } = await supabase
      .from('tickets')
      .select('id, status, assigned_to, metadata')
      .eq('id', ticketId)
      .eq('tenant_id', tenant.id)
      .single();

    if (!ticket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // Validate transition
    const allowedTransitions = transitions[ticket.status] || [];
    if (!allowedTransitions.includes(new_status)) {
      return NextResponse.json(
        { error: `Cannot transition from ${ticket.status} to ${new_status}` },
        { status: 400 }
      );
    }

    // Validate permissions
    const userPermissions = permissions[user.role] || [];
    if (!userPermissions.includes(new_status)) {
      return NextResponse.json(
        { error: `Role ${user.role} cannot set status to ${new_status}` },
        { status: 403 }
      );
    }

    // Special rule: agents can only open tickets assigned to them
    if (
      new_status === 'open' &&
      user.role === 'agent' &&
      ticket.assigned_to !== user.id
    ) {
      return NextResponse.json(
        { error: 'You can only open tickets assigned to you' },
        { status: 403 }
      );
    }

    // Update ticket
    const updateData: Record<string, unknown> = {
      status: new_status,
      updated_at: new Date().toISOString(),
    };

    if (new_status === 'resolved') {
      updateData.resolved_at = new Date().toISOString();
    }
    if (new_status === 'closed') {
      updateData.closed_at = new Date().toISOString();
    }

    // Update metadata
    const currentMetadata = (ticket.metadata as Record<string, unknown>) || {};
    updateData.metadata = {
      ...currentMetadata,
      status_changes: [
        ...((currentMetadata.status_changes as unknown[]) || []),
        {
          from: ticket.status,
          to: new_status,
          by: user.id,
          at: new Date().toISOString(),
          role: user.role,
        },
      ],
    };

    const { error } = await supabase
      .from('tickets')
      .update(updateData)
      .eq('id', ticketId)
      .eq('tenant_id', tenant.id);

    if (error) {
      return NextResponse.json(
        { error: 'Failed to update status' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Status updated to ${new_status}`,
      ticket: { id: ticketId, status: new_status },
    });
  } catch (error) {
    console.error('Status update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Minimal status check endpoint for incremental updates
 * Returns 304 Not Modified if ticket hasn't changed
 */
export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    const { ticketId } = await params;
    const { userId } = await auth();

    if (!userId) {
      return new Response(null, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenant_id');
    if (!tenantId) {
      return new Response(null, { status: 400 });
    }

    const supabase = createServiceSupabaseClient();

    // Get tenant UUID
    const { data: tenant } = await supabase
      .from('tenants')
      .select('id')
      .eq('subdomain', tenantId)
      .single();

    if (!tenant) {
      return new Response(null, { status: 404 });
    }

    // Get ticket updated_at timestamp
    const { data: ticket, error } = await supabase
      .from('tickets')
      .select('updated_at')
      .eq('id', ticketId)
      .eq('tenant_id', tenant.id)
      .single();

    if (error || !ticket) {
      return new Response(null, { status: 404 });
    }

    // Check If-Modified-Since header
    const ifModifiedSince = request.headers.get('If-Modified-Since');
    if (ifModifiedSince) {
      const clientDate = new Date(ifModifiedSince);
      const serverDate = new Date(ticket.updated_at || '');

      // If ticket hasn't been modified since client's version
      if (serverDate <= clientDate) {
        return new Response(null, {
          status: 304,
          headers: {
            'Last-Modified': serverDate.toUTCString(),
          },
        });
      }
    }

    // Ticket has been modified
    return new Response(null, {
      status: 200,
      headers: {
        'Last-Modified': new Date(ticket.updated_at || '').toUTCString(),
      },
    });
  } catch (error) {
    console.error('Status check error:', error);
    return new Response(null, { status: 500 });
  }
}
