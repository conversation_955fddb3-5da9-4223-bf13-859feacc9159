'use client';

import { QueryClient } from '@tanstack/react-query';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { useState } from 'react';
import {
  queryClient as defaultQueryClient,
  cacheManager,
} from '@/lib/query-client';

// IndexedDB persister for optimal 2025 pattern
const createPersister = () => {
  return createSyncStoragePersister({
    storage: {
      getItem: (key: string) => {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      },
      setItem: (key: string, value: unknown) => {
        localStorage.setItem(key, JSON.stringify(value));
      },
      removeItem: (key: string) => {
        localStorage.removeItem(key);
      },
    },
    key: 'TicketingApp_ReactQueryCache',
    throttleTime: 1000,
  });
};

// Cleanup old databases
const cleanupOldDatabases = async () => {
  try {
    const databases = await indexedDB.databases();
    for (const db of databases) {
      if (db.name === 'TicketingCacheDB') {
        indexedDB.deleteDatabase(db.name);
        // Old database removed successfully
      }
    }
  } catch {
    // Failed to cleanup old databases - non-critical error
  }
};

// Optimized cache cleanup for background execution
export const clearAllCaches = async () => {
  // Return immediately to prevent blocking
  return new Promise<void>((resolve) => {
    // Use microtask to ensure non-blocking execution
    queueMicrotask(async () => {
      try {
        // Clear localStorage items
        const keysToRemove: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (
            key &&
            (key.startsWith('ticketing') || key.startsWith('Ticketing'))
          ) {
            keysToRemove.push(key);
          }
        }
        keysToRemove.forEach((key) => localStorage.removeItem(key));

        // Clear TanStack Query cache
        cacheManager.clearAllCache();

        // Schedule heavy IndexedDB cleanup for later
        setTimeout(async () => {
          try {
            const { cacheDB } = await import('@/lib/cache/ticket-cache-db');
            await cacheDB.delete();
          } catch (error) {
            console.warn('IndexedDB cleanup error:', error);
          }
        }, 50);

        resolve();
      } catch (error) {
        console.warn('Cache cleanup error:', error);
        resolve(); // Always resolve to prevent blocking
      }
    });
  });
};

// CRITICAL FIX: Utility to clean up stale ticket queries (404 tickets)
export const cleanupStaleTicketQueries = (
  queryClient: QueryClient,
  tenantId: string
) => {
  try {
    // Remove queries for tickets that returned 404
    const allQueries = queryClient.getQueriesData({ queryKey: [undefined] });

    allQueries.forEach(([queryKey, data]: [unknown, unknown]) => {
      if (
        Array.isArray(queryKey) &&
        queryKey[0] === 'tickets' &&
        queryKey[1] === tenantId &&
        (queryKey[2] === 'detail' || queryKey[3] === 'messages') &&
        (data === null || data === undefined)
      ) {
        const ticketId = queryKey[2] === 'detail' ? queryKey[3] : queryKey[3];
        console.log('🧹 Cleaning up stale query for ticket:', ticketId);
        queryClient.removeQueries({ queryKey });
      }
    });
  } catch (error) {
    console.warn('Failed to cleanup stale queries:', error);
  }
};

// Optimal 2025 React Query Provider with PersistQueryClientProvider
function ReactQueryProviderInner({ children }: { children: React.ReactNode }) {
  // Use the optimized query client from our configuration
  const [queryClient] = useState(() => defaultQueryClient);

  const persister = createPersister();

  // Cleanup old databases on mount
  if (typeof window !== 'undefined') {
    cleanupOldDatabases();
  }

  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister,
        maxAge: 2 * 60 * 60 * 1000, // 2 hours - longer persistence for cache-first approach
        dehydrateOptions: {
          shouldDehydrateQuery: (query) => query.state.status === 'success',
        },
        // CRITICAL: Ensure cached data is served immediately while background validation occurs
      }}
    >
      {children}
    </PersistQueryClientProvider>
  );
}

// Main component that gets tenant context
export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ReactQueryProviderInner>{children}</ReactQueryProviderInner>;
}
