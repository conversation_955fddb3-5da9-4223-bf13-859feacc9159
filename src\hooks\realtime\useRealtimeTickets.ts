'use client';

import { useQuery } from '@tanstack/react-query';
import { QueryKeys } from '@/lib/query-keys';

interface FilterContext {
  tenantId: string;
  role: string;
  userId: string;
  email: string;
}

export function useRealtimeTickets(
  filterContext: FilterContext,
  options: { enabled: boolean }
) {
  return useQuery({
    queryKey: QueryKeys.TICKETS.list(filterContext.tenantId, {
      roleFilter: filterContext.role === 'agent' ? 'assigned' : 'all',
    }),
    queryFn: async () => {
      const { tenantId, role } = filterContext;

      // Call the API endpoint instead of querying database directly
      const params = new URLSearchParams({
        tenant_id: tenantId,
        role_filter: role === 'agent' ? 'assigned' : 'all',
      });

      // Use the lightweight cards endpoint for initial load
      const response = await fetch(`/api/tickets/cards?${params}`);

      if (!response.ok) {
        const error = await response.json();
        console.error('Error fetching ticket cards:', error);
        throw new Error(error.error || 'Failed to fetch ticket cards');
      }

      const ticketCards = await response.json();
      return ticketCards || [];
    },
    enabled: options.enabled,
    staleTime: 30 * 1000, // 30 seconds - prevents immediate refetch after optimistic updates
    gcTime: 30 * 60 * 1000, // 30 minutes
    networkMode: 'offlineFirst', // CACHE-FIRST: Use cached data when offline
    refetchOnWindowFocus: false, // Don't refetch on focus to preserve optimistic updates
    refetchOnMount: false, // CACHE-FIRST: Don't refetch on mount, use cached data
    refetchOnReconnect: 'always' as const, // Validate when network reconnects
    refetchInterval: 30 * 1000, // Poll every 30 seconds for real-time updates
    // SMART CACHE: Use placeholderData to show cached data instantly while fetching
    placeholderData: (previousData) => previousData,
  });
}
