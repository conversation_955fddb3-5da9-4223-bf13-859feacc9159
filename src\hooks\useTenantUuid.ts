/**
 * Simple hook to resolve tenant UUID from tenant ID
 * Replaces the deleted useRealtimeQuery implementation
 */

import { useQuery } from '@tanstack/react-query';
import { tenantQueryOptions } from '@/lib/query-options';

export function useTenantUuid(
  tenantId: string,
  options: { enabled?: boolean } = {}
) {
  // CRITICAL FIX: Use centralized query options to prevent duplicate fetches
  return useQuery({
    ...tenantQueryOptions.uuid(tenantId),
    enabled: !!tenantId && options.enabled !== false,
  });
}
