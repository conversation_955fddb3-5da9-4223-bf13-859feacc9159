import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { z } from 'zod';

const AssignTicketSchema = z.object({
  assigned_to: z.string().uuid().nullable(),
  tenant_id: z.string().min(1),
  reason: z.string().optional(),
});

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    const { ticketId } = await params;
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const requestBody = await request.json();
    const validationResult = AssignTicketSchema.safeParse(requestBody);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid assignment data',
          details: validationResult.error.issues,
        },
        { status: 400 }
      );
    }

    const { assigned_to, tenant_id, reason } = validationResult.data;
    const serviceSupabase = createServiceSupabaseClient();

    // Resolve tenant subdomain to UUID if needed
    let tenantUuid = tenant_id;
    if (
      !tenant_id.match(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
      )
    ) {
      const { data: tenantData, error: tenantError } = await serviceSupabase
        .from('tenants')
        .select('id')
        .eq('subdomain', tenant_id)
        .single();

      if (tenantError || !tenantData) {
        return NextResponse.json({ error: 'Invalid tenant' }, { status: 400 });
      }
      tenantUuid = tenantData.id;
    }

    // Verify user has access to this tenant and get user data
    const { data: userData, error: userError } = await serviceSupabase
      .from('users')
      .select('id, role, first_name, last_name, email')
      .eq('clerk_id', userId)
      .eq('tenant_id', tenantUuid)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Check if user has permission to assign tickets
    // Agents can only self-assign unassigned tickets
    // Admins and super_admins can assign to anyone
    const isSelfAssignment = assigned_to === userData.id;
    const canAssign =
      ['admin', 'super_admin'].includes(userData.role) ||
      (userData.role === 'agent' && isSelfAssignment);

    if (!canAssign) {
      return NextResponse.json(
        { error: 'Insufficient permissions to assign tickets' },
        { status: 403 }
      );
    }

    // Get the current ticket to update metadata
    const { data: currentTicket, error: ticketError } = await serviceSupabase
      .from('tickets')
      .select('metadata, assigned_to')
      .eq('id', ticketId)
      .eq('tenant_id', tenantUuid)
      .single();

    if (ticketError || !currentTicket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // Prepare assignment metadata
    const assignmentMetadata = {
      assigned_by: userData.id, // Database UUID
      assigned_by_clerk_id: userId, // Clerk user ID for frontend filtering
      assigned_at: new Date().toISOString(),
      assigned_by_role: userData.role,
      ...(reason && { reason }),
      // Keep history of previous assignments
      previous_assignment: currentTicket.assigned_to
        ? {
            assigned_to: currentTicket.assigned_to,
            unassigned_at: new Date().toISOString(),
            unassigned_by: userData.id,
          }
        : undefined,
    };

    // Get assigned user's Clerk ID if there's an assignment
    let assignedToClerkId = null;
    if (assigned_to) {
      const { data: assignedUser } = await serviceSupabase
        .from('users')
        .select('clerk_id')
        .eq('id', assigned_to)
        .single();

      if (assignedUser) {
        assignedToClerkId = assignedUser.clerk_id;
      }
    }

    // Update the ticket with new assignment
    const existingMetadata =
      currentTicket.metadata && typeof currentTicket.metadata === 'object'
        ? (currentTicket.metadata as Record<string, unknown>)
        : {};

    const updatedMetadata = {
      ...existingMetadata,
      assignment: assignmentMetadata,
    };

    const { data: updatedTicket, error: updateError } = await serviceSupabase
      .from('tickets')
      .update({
        assigned_to,
        assigned_to_clerk_id: assignedToClerkId, // CRITICAL FIX: Store assignee's Clerk ID for role-based filtering
        updated_at: new Date().toISOString(),
        metadata: updatedMetadata,
      })
      .eq('id', ticketId)
      .eq('tenant_id', tenantUuid)
      .select('*')
      .single();

    if (updateError) {
      console.error('Failed to update ticket assignment:', updateError);
      return NextResponse.json(
        { error: 'Failed to update assignment' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: assigned_to
        ? 'Ticket assigned successfully'
        : 'Ticket unassigned successfully',
      ticket: updatedTicket,
      assignment: assignmentMetadata,
    });
  } catch (error) {
    console.error('Unexpected error in ticket assignment:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
