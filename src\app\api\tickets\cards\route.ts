import { auth, currentUser } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';
import { SupabaseClient } from '@supabase/supabase-js';

// Helper functions
async function getTenantUuid(
  serviceSupabase: SupabaseClient,
  tenantParam: string
) {
  if (
    !tenantParam.match(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    )
  ) {
    const { data: tenantData, error: tenantError } = await serviceSupabase
      .from('tenants')
      .select('id')
      .eq('subdomain', tenantParam)
      .single();

    if (tenantError || !tenantData) {
      throw new Error(`Tenant '${tenantParam}' not found`);
    }
    return tenantData.id;
  }
  return tenantParam;
}

async function validateUserAccess(
  serviceSupabase: SupabaseClient,
  userId: string,
  tenantUuid: string
) {
  const { data: userData, error: userError } = await serviceSupabase
    .from('users')
    .select('id, email, first_name, last_name, role')
    .eq('clerk_id', userId)
    .eq('tenant_id', tenantUuid)
    .single();

  if (userError || !userData) {
    throw new Error('User not found in tenant');
  }

  return userData;
}

/**
 * Lightweight ticket cards endpoint - returns only essential fields for list view
 * This enables lazy loading of full ticket details on demand
 */
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantParam = searchParams.get('tenant_id');
    const roleFilter = searchParams.get('role_filter') || 'all';

    if (!tenantParam) {
      return NextResponse.json(
        { error: 'tenant_id is required' },
        { status: 400 }
      );
    }

    // Get user role from Clerk
    const user = await currentUser();
    const clerkUserRole = (() => {
      switch (user?.publicMetadata?.role) {
        case 'org:super_admin':
          return 'super_admin';
        case 'org:admin':
          return 'admin';
        case 'org:agent':
          return 'agent';
        case 'org:member':
          return 'user';
        default:
          return null;
      }
    })();

    const serviceSupabase = createServiceSupabaseClient();
    const tenantUuid = await getTenantUuid(serviceSupabase, tenantParam);
    const userData = await validateUserAccess(
      serviceSupabase,
      userId,
      tenantUuid
    );

    const userRole = clerkUserRole || userData.role || 'user';

    // Build the base query - only select fields needed for cards
    let ticketsQuery = serviceSupabase
      .from('tickets')
      .select(
        `
        id,
        title,
        description,
        status,
        priority,
        department,
        created_at,
        updated_at,
        created_by,
        assigned_to,
        users!tickets_created_by_fkey (
          id,
          first_name,
          last_name,
          email,
          avatar_url
        ),
        assigned_user:users!tickets_assigned_to_fkey (
          clerk_id
        )
      `
      )
      .eq('tenant_id', tenantUuid);

    // Apply role-based filtering
    const dbUserId = userData.id;

    switch (userRole) {
      case 'super_admin':
      case 'admin':
        if (roleFilter === 'assigned') {
          ticketsQuery = ticketsQuery.eq('assigned_to', dbUserId);
        } else if (roleFilter === 'created') {
          ticketsQuery = ticketsQuery.eq('created_by', dbUserId);
        }
        break;

      case 'agent':
        if (roleFilter === 'assigned') {
          ticketsQuery = ticketsQuery.eq('assigned_to', dbUserId);
        } else if (roleFilter === 'created') {
          ticketsQuery = ticketsQuery.eq('created_by', dbUserId);
        }
        break;

      case 'user':
        ticketsQuery = ticketsQuery.eq('created_by', dbUserId);
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid user role' },
          { status: 403 }
        );
    }

    // Order by most recent first
    ticketsQuery = ticketsQuery.order('created_at', { ascending: false });

    const { data: tickets, error } = await ticketsQuery;

    if (error) {
      console.error('Error fetching ticket cards:', error);
      return NextResponse.json(
        { error: 'Failed to fetch tickets' },
        { status: 500 }
      );
    }

    // Transform tickets to card format
    const ticketCards = (tickets || []).map((ticket) => {
      // Transform directly to card format with only essential fields
      const ticketCard = {
        id: ticket.id,
        title: ticket.title,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        department: ticket.department,
        createdAt: new Date(ticket.created_at || Date.now()),
        updatedAt: new Date(ticket.updated_at || Date.now()),
        userId: ticket.created_by,
        userName:
          `${ticket.users?.first_name || ''} ${
            ticket.users?.last_name || ''
          }`.trim() || 'Unknown User',
        userEmail: ticket.users?.email || '<EMAIL>',
        userAvatar: ticket.users?.avatar_url || null,
        assignedTo: ticket.assigned_to,
        assignedToClerkId: ticket.assigned_user?.clerk_id || null,
      };
      return ticketCard;
    });

    return NextResponse.json(ticketCards);
  } catch (error) {
    console.error('Ticket cards fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
