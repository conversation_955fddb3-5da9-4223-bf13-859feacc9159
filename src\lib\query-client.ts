/**
 * Query Client Configuration - 2025 Optimized
 *
 * Configured for:
 * - Cache-first loading with instant display
 * - Background refresh for data validation
 * - 30-minute smart cache expiration
 * - Optimistic UI support
 */

import { QueryClient } from '@tanstack/react-query';
import { CacheManager } from './cache/cache-manager';

// Create query client with optimized defaults
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache-first strategy: serve from cache instantly
      // NOTE: Don't set global staleTime here - let individual queries define their own
      gcTime: 30 * 60 * 1000, // 30 minutes garbage collection

      // Retry configuration
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error instanceof Error && error.message.includes('4')) {
          return false;
        }
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),

      // Network behavior
      networkMode: 'offlineFirst', // Try cache first, then network

      // Refetch behavior - let individual queries override these
      refetchOnWindowFocus: false, // Default to false for cache-first
      refetchOnMount: false, // Default to false for cache-first
      refetchOnReconnect: 'always',

      // Performance optimizations
      structuralSharing: true, // Only re-render changed parts
      notifyOnChangeProps: 'all', // Fine-grained reactivity
    },

    mutations: {
      // Optimistic update configuration
      networkMode: 'offlineFirst',
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});

// Initialize cache manager
export const cacheManager = CacheManager.getInstance(queryClient);

// Export utility functions for cache management
export const cacheUtils = {
  /**
   * Prefetch ticket data for instant navigation
   */
  prefetchTicket: async (tenantId: string, ticketId: string) => {
    await cacheManager.prefetchData(
      ['tickets', tenantId, 'detail', ticketId],
      async () => {
        const response = await fetch(
          `/api/tickets/${ticketId}?tenant_id=${tenantId}`
        );
        if (!response.ok) throw new Error('Failed to fetch ticket');
        const data = await response.json();
        return data.data;
      }
    );
  },

  /**
   * Prefetch ticket messages for instant display
   */
  prefetchMessages: async (tenantId: string, ticketId: string) => {
    await cacheManager.prefetchData(
      ['tickets', tenantId, 'detail', ticketId, 'messages'],
      async () => {
        const response = await fetch(
          `/api/tickets/${ticketId}/messages?tenant_id=${tenantId}`
        );
        if (!response.ok) throw new Error('Failed to fetch messages');
        const data = await response.json();
        return data.messages || [];
      }
    );
  },

  /**
   * Get cache statistics
   */
  getCacheStats: () => cacheManager.getCacheStats(),

  /**
   * Force refresh specific data
   */
  refreshTickets: async (tenantId: string) => {
    await cacheManager.refreshCache(['tickets', tenantId]);
  },

  /**
   * Clear all cache (use sparingly)
   */
  clearCache: () => cacheManager.clearAllCache(),
};
