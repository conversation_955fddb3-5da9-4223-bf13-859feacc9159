/**
 * Cache Manager - 2025 TanStack Query Integration
 *
 * Provides intelligent cache management with:
 * - 30-minute smart expiration with incremental eviction
 * - Cache-first loading with instant refresh
 * - Seamless integration with real-time updates
 * - Optimistic UI support
 */

import { QueryClient } from '@tanstack/react-query';

export class CacheManager {
  private static instance: CacheManager;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor(private queryClient: QueryClient) {
    this.startIncrementalCleanup();
  }

  static getInstance(queryClient: QueryClient): CacheManager {
    if (!this.instance) {
      this.instance = new CacheManager(queryClient);
    }
    return this.instance;
  }

  /**
   * Start incremental cleanup process for 30-minute expiration
   */
  private startIncrementalCleanup(): void {
    // Run cleanup every 5 minutes
    this.cleanupInterval = setInterval(
      () => {
        this.performIncrementalCleanup();
      },
      5 * 60 * 1000
    );
  }

  /**
   * Perform incremental cache cleanup
   * Removes oldest entries beyond 30-minute threshold
   */
  private performIncrementalCleanup(): void {
    const now = Date.now();
    const thirtyMinutesAgo = now - 30 * 60 * 1000;

    // Get all queries
    const queries = this.queryClient.getQueryCache().getAll();

    // Sort by last update time (oldest first)
    const sortedQueries = queries.sort(
      (a, b) => (a.state.dataUpdatedAt || 0) - (b.state.dataUpdatedAt || 0)
    );

    // Remove queries older than 30 minutes that aren't actively being used
    let removedCount = 0;
    for (const query of sortedQueries) {
      if ((query.state.dataUpdatedAt || 0) < thirtyMinutesAgo) {
        // Only remove if not actively being observed
        if (query.getObserversCount() === 0) {
          this.queryClient.removeQueries({ queryKey: query.queryKey });
          removedCount++;
        }
      }
    }

    if (removedCount > 0) {
      console.log(`🧹 Cache cleanup: Removed ${removedCount} expired queries`);
    }
  }

  /**
   * Force cache refresh for specific queries
   * Used when real-time updates indicate changes
   */
  async refreshCache(queryKey: unknown[]): Promise<void> {
    await this.queryClient.invalidateQueries({
      queryKey,
      refetchType: 'active', // Only refetch if component is mounted
    });
  }

  /**
   * Prefetch data for improved performance
   */
  async prefetchData<T>(
    queryKey: unknown[],
    queryFn: () => Promise<T>,
    staleTime = 0
  ): Promise<void> {
    await this.queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime,
    });
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): {
    totalQueries: number;
    activeQueries: number;
    staleQueries: number;
    cacheSize: number;
  } {
    const queries = this.queryClient.getQueryCache().getAll();
    const now = Date.now();

    return {
      totalQueries: queries.length,
      activeQueries: queries.filter((q) => q.getObserversCount() > 0).length,
      staleQueries: queries.filter(
        (q) => q.state.data && now - (q.state.dataUpdatedAt || 0) > 0
      ).length,
      cacheSize: queries.reduce((size, query) => {
        // Estimate size based on stringified data
        const dataSize = query.state.data
          ? JSON.stringify(query.state.data).length
          : 0;
        return size + dataSize;
      }, 0),
    };
  }

  /**
   * Clear all cache (use with caution)
   */
  clearAllCache(): void {
    this.queryClient.clear();
    console.log('🗑️ All cache cleared');
  }

  /**
   * Stop cleanup process
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}
