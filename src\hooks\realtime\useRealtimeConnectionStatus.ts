'use client';

import { useState, useEffect } from 'react';
import { useSupabaseClient } from '@/lib/supabase-clerk';

interface ConnectionStatus {
  isConnected: boolean;
  lastConnected: Date | null;
}

export function useRealtimeConnectionStatus(
  tenantId: string
): ConnectionStatus | null {
  const { supabase } = useSupabaseClient();
  const [status, setStatus] = useState<ConnectionStatus | null>(() => {
    // Check if we should simulate disconnected state on initial load
    if (
      typeof window !== 'undefined' &&
      window.localStorage.getItem('DEBUG_REALTIME_OFF') === 'true'
    ) {
      return {
        isConnected: false,
        lastConnected: null,
      };
    }
    return null;
  });

  useEffect(() => {
    if (!tenantId) return;

    // Check if we should simulate disconnected state
    if (
      typeof window !== 'undefined' &&
      window.localStorage.getItem('DEBUG_REALTIME_OFF') === 'true'
    ) {
      setStatus({
        isConnected: false,
        lastConnected: null,
      });
      return;
    }

    // Monitor realtime connection status
    const channel = supabase.channel('connection-status');

    channel
      .on('system', { event: '*' }, (payload) => {
        if (payload.event === 'connected') {
          setStatus({
            isConnected: true,
            lastConnected: new Date(),
          });
        } else if (payload.event === 'disconnected') {
          setStatus((prev) => ({
            isConnected: false,
            lastConnected: prev?.lastConnected || null,
          }));
        }
      })
      .subscribe((status) => {
        setStatus({
          isConnected: status === 'SUBSCRIBED',
          lastConnected: status === 'SUBSCRIBED' ? new Date() : null,
        });
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [tenantId, supabase]);

  return status;
}
