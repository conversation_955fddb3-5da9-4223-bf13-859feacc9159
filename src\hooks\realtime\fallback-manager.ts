/**
 * Real-time Fallback Manager - 2025 Smart Recovery Pattern
 *
 * Handles connection failures with smart incremental updates.
 * Only fetches changed data when connection is lost.
 */

import { QueryClient } from '@tanstack/react-query';
import { QueryKeys } from '@/lib/query-keys';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';

export class FallbackManager {
  private fallbackActive = false;
  private checkInterval: NodeJS.Timeout | null = null;

  constructor(
    private queryClient: QueryClient,
    private tenantId: string
  ) {}

  /**
   * Start fallback mode when connection is lost
   */
  startFallback(): void {
    if (this.fallbackActive) return;

    console.log('🔄 Starting fallback mode for tenant:', this.tenantId);
    this.fallbackActive = true;

    // Check every 30 seconds for changes
    this.checkInterval = setInterval(() => {
      this.performIncrementalSync();
    }, 30000);
  }

  /**
   * Stop fallback mode when connection is restored
   */
  stopFallback(): void {
    if (!this.fallbackActive) return;

    console.log('✅ Stopping fallback mode for tenant:', this.tenantId);
    this.fallbackActive = false;

    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  /**
   * Validate single ticket on user interaction (click)
   */
  async validateSingleTicket(ticketId: string): Promise<void> {
    if (!this.fallbackActive) return;

    console.log('🎯 Validating ticket:', ticketId);

    try {
      // Fetch latest data for this specific ticket
      const response = await fetch(
        `/api/tickets/${ticketId}?tenant_id=${this.tenantId}`
      );

      if (!response.ok) {
        if (response.status === 404) {
          // Ticket was deleted - remove from cache
          this.queryClient.removeQueries({
            queryKey: QueryKeys.TICKETS.detail(this.tenantId, ticketId),
          });
        }
        return;
      }

      const data = await response.json();
      const serverTicket = data.data;

      // Get cached data
      const cachedData = this.queryClient.getQueryData(
        QueryKeys.TICKETS.detail(this.tenantId, ticketId)
      );

      if (cachedData) {
        const cachedTicket = Array.isArray(cachedData)
          ? cachedData[0]
          : cachedData;

        // Check if data changed
        if (this.hasTicketChanged(cachedTicket, serverTicket)) {
          // Update cache with new data
          this.queryClient.setQueryData(
            QueryKeys.TICKETS.detail(this.tenantId, ticketId),
            serverTicket
          );

          // Also invalidate list to ensure consistency
          this.queryClient.invalidateQueries({
            queryKey: ['tickets', this.tenantId, 'list'],
            exact: false,
          });
        }
      }
    } catch (error) {
      console.error('Failed to validate ticket:', error);
    }
  }

  /**
   * Perform incremental sync for active queries
   */
  private async performIncrementalSync(): Promise<void> {
    if (!this.fallbackActive) return;

    // Get all active ticket queries
    const activeQueries = this.queryClient.getQueryCache().findAll({
      queryKey: ['tickets', this.tenantId],
      type: 'active',
    });

    // Validate each active query
    for (const query of activeQueries) {
      const queryKey = query.queryKey;

      // Extract ticket ID if it's a detail query
      if (queryKey.includes('detail') && queryKey.length > 3) {
        const ticketId = queryKey[queryKey.length - 1] as string;
        await this.validateSingleTicket(ticketId);
      }
    }
  }

  /**
   * Check if ticket data has changed - supports incremental updates
   */
  private hasTicketChanged(cached: Ticket, server: Ticket): boolean {
    // Comprehensive field checking for all possible changes
    const fieldsToCheck: (keyof Ticket)[] = [
      'status',
      'priority',
      'assignedTo',
      'assignedToClerkId',
      'title',
      'description',
      'department',
      'tags',
      'resolvedAt',
      'closedAt',
      'dueDate',
      'updatedAt',
    ];

    return fieldsToCheck.some((field) => {
      const cachedValue = cached[field];
      const serverValue = server[field];

      // Handle date comparison
      if (field.includes('At') || field === 'dueDate') {
        const isValidDateValue = (
          value: unknown
        ): value is string | number | Date =>
          value instanceof Date ||
          typeof value === 'string' ||
          typeof value === 'number';

        const cachedTime =
          cachedValue && isValidDateValue(cachedValue)
            ? new Date(cachedValue).getTime()
            : null;
        const serverTime =
          serverValue && isValidDateValue(serverValue)
            ? new Date(serverValue).getTime()
            : null;
        return cachedTime !== serverTime;
      }

      // Handle array comparison (tags)
      if (Array.isArray(cachedValue) && Array.isArray(serverValue)) {
        return JSON.stringify(cachedValue) !== JSON.stringify(serverValue);
      }

      return cachedValue !== serverValue;
    });
  }

  /**
   * Force validation of all visible data
   */
  async forceValidation(): Promise<void> {
    console.log('🔍 Force validation triggered');
    await this.performIncrementalSync();
  }
}
