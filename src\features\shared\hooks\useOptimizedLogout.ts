'use client';

import { useCallback, useRef, useTransition } from 'react';
import { useClerk } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { useTicketingUIActions } from '@/features/ticketing/store/use-ticketing-store';
import { useUIActions } from '@/stores/ui-store';
import { resetGlobalSupabaseClient } from '@/lib/supabase-clerk';

/**
 * Optimized logout hook with 2025 performance patterns
 * - Non-blocking async operations
 * - Selective data cleanup (preserves theme/UI settings)
 * - Instant UI response (< 50ms)
 * - Progressive cleanup in background
 */
export function useOptimizedLogout() {
  const { signOut: clerkSignOut } = useClerk();
  const router = useRouter();
  const queryClient = useQueryClient();
  const resetTicketingUIState = useTicketingUIActions.useResetUIState();
  const resetUIState = useUIActions.useResetUIState();
  const [isPending, startTransition] = useTransition();
  const cleanupRef = useRef<Promise<void> | null>(null);

  const signOut = useCallback(async () => {
    // Prevent multiple logout attempts
    if (cleanupRef.current) return;

    // Set global logout flag immediately to prevent race conditions
    (globalThis as Record<string, unknown>)._clerkLogoutInProgress = true;

    // Phase 1: Instant UI updates (< 10ms)
    // Reset user-specific UI state, preserving theme/layout preferences
    resetTicketingUIState(); // Reset ticketing-specific state
    resetUIState(); // Reset general UI state

    // Phase 2: Cancel active operations
    queryClient.cancelQueries();

    // Phase 3: Navigate immediately for instant feedback
    startTransition(() => {
      router.push('/sign-in');
    });

    // Phase 4: Smart background cleanup (non-blocking)
    cleanupRef.current = (async () => {
      try {
        // 1. Close real-time connections first
        resetGlobalSupabaseClient();

        // 2. Smart React Query cache cleanup
        // Remove all user-specific data but keep structure
        queryClient.removeQueries({
          predicate: (query) => {
            const key = query.queryKey;
            // Keep theme/settings queries
            if (
              Array.isArray(key) &&
              (key[0] === 'theme' ||
                key[0] === 'ui-settings' ||
                key[0] === 'departments') // Keep department colors
            ) {
              return false;
            }
            return true; // Remove everything else
          },
        });

        // 3. Selective localStorage cleanup
        const preserveKeys = [
          'theme',
          'color-scheme',
          'ui-preferences',
          'sidebar-state',
          'language',
          'accessibility',
        ];

        const keysToRemove: string[] = [];
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key) {
            // Check if key should be preserved
            const shouldPreserve = preserveKeys.some((preserve) =>
              key.toLowerCase().includes(preserve)
            );

            if (
              !shouldPreserve &&
              (key.includes('ticket') ||
                key.includes('user') ||
                key.includes('auth') ||
                key.includes('clerk') ||
                key.includes('supabase') ||
                key.includes('query-cache'))
            ) {
              keysToRemove.push(key);
            }
          }
        }

        // Remove in batch for better performance
        keysToRemove.forEach((key) => localStorage.removeItem(key));

        // 4. Selective IndexedDB cleanup using Dexie
        try {
          const { cacheDB } = await import('@/lib/cache/ticket-cache-db');

          // Clear user data tables only, in parallel for speed
          await Promise.all([
            cacheDB.tickets.clear(),
            cacheDB.messages.clear(),
            cacheDB.userProfiles.clear(),
            cacheDB.attachments.clear(),
            // Keep queryMetadata as it might contain UI preferences
          ]);

          // Don't delete the entire database - keep structure
        } catch (error) {
          // Non-critical error, continue logout
          console.debug('IndexedDB cleanup skipped:', error);
        }

        // 5. Perform Clerk signout (this is the slowest part)
        await clerkSignOut();

        // 6. Final cleanup in next tick (non-blocking)
        setTimeout(() => {
          // Clear any remaining auth tokens
          document.cookie.split(';').forEach((c) => {
            if (c.includes('__session') || c.includes('auth')) {
              document.cookie = c
                .replace(/^ +/, '')
                .replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);
            }
          });
        }, 0);
      } catch (error) {
        console.error('Logout cleanup error:', error);
        // Don't block logout on errors - force navigation
        window.location.href = '/sign-in';
      } finally {
        (globalThis as Record<string, unknown>)._clerkLogoutInProgress = false;
        cleanupRef.current = null;
      }
    })();

    // Don't await cleanup - let it run in background
  }, [clerkSignOut, router, resetTicketingUIState, resetUIState, queryClient]);

  return {
    signOut,
    isLoggingOut: isPending || cleanupRef.current !== null,
  };
}
