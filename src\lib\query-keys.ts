/**
 * Centralized Query Keys Factory - 2025 Optimized
 *
 * All query keys must include tenant_id as first element after the resource name
 * for proper tenant isolation and cache separation.
 */

export interface TicketFilters {
  status?: string[];
  priority?: string[];
  assignedTo?: string;
  createdBy?: string;
  roleFilter?: 'new' | 'assigned' | 'all';
}

export interface UserFilters {
  role?: string[];
  status?: string[];
  search?: string;
}

export const QueryKeys = {
  // Tickets
  TICKETS: {
    all: (tenantId: string) => ['tickets', tenantId] as const,
    list: (tenantId: string, filters?: TicketFilters) =>
      [...QueryKeys.TICKETS.all(tenantId), 'list', filters] as const,
    detail: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.all(tenantId), 'detail', ticketId] as const,
    messages: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.detail(tenantId, ticketId), 'messages'] as const,
    attachments: (tenantId: string, ticketId: string) =>
      [...QueryKeys.TICKETS.detail(tenantId, ticketId), 'attachments'] as const,
  },

  // Users
  USERS: {
    all: (tenantId: string) => ['users', tenantId] as const,
    list: (tenantId: string, filters?: UserFilters) =>
      [...QueryKeys.USERS.all(tenantId), 'list', filters] as const,
    detail: (tenantId: string, userId: string) =>
      [...QueryKeys.USERS.all(tenantId), 'detail', userId] as const,
    search: (tenantId: string, query: string) =>
      [...QueryKeys.USERS.all(tenantId), 'search', query] as const,
  },

  // Tenant-specific data
  TENANT: {
    all: (tenantId: string) => ['tenant', tenantId] as const,
    settings: (tenantId: string) =>
      [...QueryKeys.TENANT.all(tenantId), 'settings'] as const,
    stats: (tenantId: string) =>
      [...QueryKeys.TENANT.all(tenantId), 'stats'] as const,
  },

  // Real-time data (shorter cache times)
  REALTIME: {
    all: (tenantId: string) => ['realtime', tenantId] as const,
    notifications: (tenantId: string, userId: string) =>
      [...QueryKeys.REALTIME.all(tenantId), 'notifications', userId] as const,
    presence: (tenantId: string) =>
      [...QueryKeys.REALTIME.all(tenantId), 'presence'] as const,
  },
} as const;

/**
 * Helper function to invalidate all queries for a specific tenant
 * Useful for logout or tenant switching scenarios
 */
export const getTenantQueryPattern = (tenantId: string) => ({
  predicate: (query: { queryKey: unknown }) => {
    const queryKey = query.queryKey;
    return Array.isArray(queryKey) && queryKey.includes(tenantId);
  },
});

/**
 * Cache timing configurations - 2025 TanStack Query Native Strategy
 * Cache-first approach with smart incremental updates and phantom message prevention
 * ✨ MODERNIZED: Leverages TanStack Query v5 native features for optimal performance
 */
export const CACHE_CONFIG = {
  // Main ticket data - cache-first with smart staleness detection
  tickets: {
    staleTime: 30 * 1000, // 30 seconds - optimal balance for ticket data
    gcTime: 30 * 60 * 1000, // 30 minutes - keep in memory for fast access
    refetchOnWindowFocus: 'always' as const, // Keep data fresh when user returns
    refetchOnMount: 'always' as const, // Always refetch for consistency
    refetchOnReconnect: 'always' as const, // Sync after network reconnection
    // ✨ TanStack Query v5 native features
    structuralSharing: true, // Only re-render changed components
    notifyOnChangeProps: ['data', 'error'], // Prevent unnecessary loading states
    retry: (failureCount: number, error: unknown) => {
      // Don't retry 404 errors - ticket doesn't exist
      if (error instanceof Error && error.message.includes('404')) return false;
      return failureCount < 2;
    },
  },

  // Messages - phantom message prevention with structural sharing
  messages: {
    staleTime: 10 * 1000, // 10 seconds - messages need frequent updates
    gcTime: 30 * 60 * 1000, // 30 minutes - consistent with ticket cache
    refetchOnWindowFocus: 'always' as const, // Keep messages fresh
    refetchOnMount: 'always' as const, // Ensure fresh message data
    refetchOnReconnect: 'always' as const, // Sync after reconnection
    // ✨ Phantom message prevention
    structuralSharing: true, // Native deduplication via structural sharing
    notifyOnChangeProps: ['data', 'error'], // Smooth message updates
    retry: (failureCount: number, error: unknown) => {
      if (error instanceof Error && error.message.includes('404')) return false;
      return failureCount < 1; // Fewer retries for messages
    },
  },

  // User data - stable with long cache times
  users: {
    staleTime: 5 * 60 * 1000, // 5 minutes - user data is stable
    gcTime: 60 * 60 * 1000, // 1 hour - users change infrequently
    refetchOnWindowFocus: false, // Avoid excessive user data requests
    refetchOnMount: 'always' as const, // Keep user data fresh
    refetchOnReconnect: 'always' as const, // Sync after reconnection
    structuralSharing: true,
    notifyOnChangeProps: ['data', 'error'],
  },

  // Settings - very stable data with long cache
  settings: {
    staleTime: 30 * 60 * 1000, // 30 minutes - settings rarely change
    gcTime: 8 * 60 * 60 * 1000, // 8 hours - keep settings cached long
    refetchOnWindowFocus: false, // Settings change infrequently
    refetchOnMount: 'always' as const, // Ensure up-to-date settings
    refetchOnReconnect: 'always' as const, // Sync after reconnection
    structuralSharing: true,
    notifyOnChangeProps: ['data', 'error'],
  },

  // ✨ NEW: Real-time integration config
  realtime: {
    // When real-time updates cache, prevent refetch for this duration
    mutationCacheTime: 5 * 1000, // 5 seconds
    // Invalidation patterns for real-time events
    invalidateAfterMutation: true,
  },
} as const;

/**
 * Simple retry and timeout configurations
 */
export const RETRY_CONFIG = {
  defaultRetries: 3,
  retryDelay: 1000, // 1 second
  mutationTimeout: 10000, // 10 seconds
} as const;
