import { z } from 'zod';
import type { Ticket } from './ticket.schema';

/**
 * Type for ticket data that may include user information
 */
type TicketWithUserInfo = Partial<Ticket> & {
  id: string;
  title: string;
  description: string;
  status: Ticket['status'];
  priority: Ticket['priority'];
  department: Ticket['department'];
  createdAt: Date | string;
  updatedAt: Date | string;
  userId?: string;
  created_by?: string;
  userName?: string;
  userEmail?: string;
  userAvatar?: string | null;
  assignedTo?: string | null;
  assigned_to?: string | null;
  assignedToClerkId?: string | null;
  isOptimistic?: boolean;
  users?: {
    first_name?: string;
    last_name?: string;
    email?: string;
    avatar_url?: string | null;
  };
  assigned_user?: {
    clerk_id?: string;
  };
};

/**
 * Lightweight ticket card schema for list views
 * Contains only essential fields needed for display in ticket cards
 */
export const TicketCardSchema = z.object({
  id: z.string().uuid(),
  title: z.string(),
  description: z.string(),
  status: z.enum(['new', 'open', 'pending', 'resolved', 'closed']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  department: z.enum(['sales', 'support', 'technical', 'marketing']),
  createdAt: z
    .union([z.string(), z.date()])
    .transform((val) => (val instanceof Date ? val : new Date(val))),
  updatedAt: z
    .union([z.string(), z.date()])
    .transform((val) => (val instanceof Date ? val : new Date(val))),

  // User info for display
  userId: z.string().uuid(),
  userName: z.string(),
  userEmail: z.string().email(),
  userAvatar: z.string().nullable().optional(),

  // Assignment info (needed for agent open button)
  assignedTo: z.string().uuid().nullable().optional(),
  assignedToClerkId: z.string().nullable().optional(),

  // Optional fields for optimistic updates
  isOptimistic: z.boolean().optional(),
});

export type TicketCard = z.infer<typeof TicketCardSchema>;

// Transform function to extract card data from full ticket
export function extractTicketCardData(
  fullTicket: TicketWithUserInfo
): TicketCard {
  return {
    id: fullTicket.id,
    title: fullTicket.title,
    description: fullTicket.description,
    status: fullTicket.status,
    priority: fullTicket.priority,
    department: fullTicket.department,
    createdAt: fullTicket.createdAt,
    updatedAt: fullTicket.updatedAt,
    userId: fullTicket.userId || fullTicket.created_by || '',
    userName:
      fullTicket.userName ||
      `${fullTicket.users?.first_name || ''} ${fullTicket.users?.last_name || ''}`.trim() ||
      'Unknown User',
    userEmail:
      fullTicket.userEmail || fullTicket.users?.email || '<EMAIL>',
    userAvatar: fullTicket.userAvatar || fullTicket.users?.avatar_url,
    assignedTo: fullTicket.assignedTo || fullTicket.assigned_to,
    assignedToClerkId:
      fullTicket.assignedToClerkId || fullTicket.assigned_user?.clerk_id,
    isOptimistic: fullTicket.isOptimistic,
  };
}
