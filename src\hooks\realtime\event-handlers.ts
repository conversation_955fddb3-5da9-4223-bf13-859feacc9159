/**
 * Real-time Event Handlers - 2025 TanStack Query Native Integration
 *
 * ✨ ENHANCED: Optimized for TanStack Query v5 with smart refetch prevention
 * ✨ PHANTOM MESSAGE FIX: Uses structural sharing instead of manual cache merging
 * ✨ PERFORMANCE: Prevents unnecessary refetches after real-time updates
 */

import { QueryClient } from '@tanstack/react-query';
import {
  RealtimePostgresChangesPayload,
  RealtimeChannel,
} from '@supabase/supabase-js';
import { QueryKeys } from '@/lib/query-keys';
import type { Database } from '@/types/supabase';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';
import { DexieCacheManager } from '@/lib/cache/dexie-cache-manager';

type TicketRow = Database['public']['Tables']['tickets']['Row'];
type MessageRow = Database['public']['Tables']['ticket_messages']['Row'];
type UserRow = Database['public']['Tables']['users']['Row'];

export class RealtimeEventHandlers {
  // Track last update times for incremental sync
  private lastUpdateTimes = new Map<string, number>();
  private cacheManager: DexieCacheManager;

  constructor(
    private queryClient: QueryClient,
    private tenantId: string
  ) {
    this.cacheManager = new DexieCacheManager(queryClient);
  }

  /**
   * Handle ticket changes - simple invalidation with incremental tracking
   */
  handleTicketChange(payload: RealtimePostgresChangesPayload<TicketRow>): void {
    const { eventType, new: newRow, old: oldRow } = payload;

    // Type guard for row data
    const hasId = (row: unknown): row is { id: string } =>
      typeof row === 'object' &&
      row !== null &&
      'id' in row &&
      typeof (row as { id: unknown }).id === 'string';

    const ticketId = hasId(newRow)
      ? newRow.id
      : hasId(oldRow)
        ? oldRow.id
        : undefined;

    console.log(`🎫 Ticket ${eventType}:`, ticketId);
    if (ticketId) {
      this.lastUpdateTimes.set(`ticket-${ticketId}`, Date.now());
    }

    // Smart invalidation based on event type
    if (eventType === 'DELETE' && ticketId) {
      // For DELETE, remove from cache completely
      this.queryClient.removeQueries({
        queryKey: QueryKeys.TICKETS.detail(this.tenantId, ticketId),
      });
      this.queryClient.removeQueries({
        queryKey: QueryKeys.TICKETS.messages(this.tenantId, ticketId),
      });
    }

    // Convert TicketRow to Ticket type for cache updates
    const newTicket = newRow as unknown as Ticket;

    // For UPDATE, directly update the cache instead of invalidating
    if (eventType === 'UPDATE' && ticketId && newTicket) {
      // Update ticket in detail cache
      this.queryClient.setQueryData(
        QueryKeys.TICKETS.detail(this.tenantId, ticketId),
        (old: Ticket | Ticket[] | undefined) => {
          if (!old) return newTicket;

          // Handle both single ticket and array formats
          const isArray = Array.isArray(old);
          const currentTicket = isArray ? old[0] : old;

          // If the ticket hasn't changed materially, don't update
          if (currentTicket && this.areTicketsEqual(currentTicket, newTicket)) {
            return old;
          }

          return isArray ? [newTicket] : newTicket;
        }
      );

      // Update ticket in list caches - CRITICAL FIX: Include all possible query key variations
      const listQueryKeys = [
        ['tickets', this.tenantId, 'list'],
        ['realtime-tickets', this.tenantId],
        QueryKeys.TICKETS.list(this.tenantId, { roleFilter: 'assigned' }),
        QueryKeys.TICKETS.list(this.tenantId, { roleFilter: 'all' }),
        QueryKeys.TICKETS.list(this.tenantId, { roleFilter: 'new' }),
      ];

      // CRITICAL FIX: Update all matching queries, not just exact matches
      listQueryKeys.forEach((queryKey) => {
        this.queryClient.setQueriesData(
          { queryKey: queryKey, exact: false },
          (old: Ticket[] | undefined) => {
            if (!old || !Array.isArray(old)) return old;

            // Update the ticket in the list
            const updatedList = old.map((ticket) =>
              ticket.id === ticketId ? { ...ticket, ...newTicket } : ticket
            );

            // CRITICAL FIX: Force React to detect the change by creating a new array reference
            return [...updatedList];
          }
        );
      });

      // ✨ SMART REFETCH PREVENTION: Mark ticket as recently updated by real-time
      // This prevents immediate refetch after real-time update
      this.preventRefetchFor(ticketId, 'ticket');

      // SMART CACHE: Persist the updated ticket to IndexedDB
      this.cacheManager.persistCache(this.tenantId);
    }

    // For INSERT, add to list caches
    if (eventType === 'INSERT' && ticketId && newTicket) {
      const listQueryKeys = [
        ['tickets', this.tenantId, 'list'],
        QueryKeys.TICKETS.list(this.tenantId, { roleFilter: 'all' }),
      ];

      listQueryKeys.forEach((queryKey) => {
        this.queryClient.setQueryData(queryKey, (old: Ticket[] | undefined) => {
          if (!old) return [newTicket];
          // Check if ticket already exists (optimistic update)
          if (old.some((t) => t.id === ticketId)) {
            return old;
          }
          return [newTicket, ...old];
        });
      });
    }

    // Only invalidate if we don't have the new data
    if (!newTicket && (eventType === 'UPDATE' || eventType === 'INSERT')) {
      this.queryClient.invalidateQueries({
        queryKey: ['tickets', this.tenantId],
        refetchType: 'active',
        exact: false,
      });
    }
  }

  /**
   * Handle message changes - simple invalidation
   */
  handleMessageChange(
    payload: RealtimePostgresChangesPayload<MessageRow>
  ): void {
    const { eventType, new: newRow, old: oldRow } = payload;

    // Type guard for message row data
    const hasTicketId = (row: unknown): row is { ticket_id: string } =>
      typeof row === 'object' &&
      row !== null &&
      'ticket_id' in row &&
      typeof (row as { ticket_id: unknown }).ticket_id === 'string';

    const ticketId = hasTicketId(newRow)
      ? newRow.ticket_id
      : hasTicketId(oldRow)
        ? oldRow.ticket_id
        : undefined;

    if (!ticketId) return;

    console.log(`💬 Message ${eventType} for ticket:`, ticketId);

    // For INSERT, check if this is replacing an optimistic message
    if (eventType === 'INSERT' && newRow) {
      const currentMessages = this.queryClient.getQueryData(
        QueryKeys.TICKETS.messages(this.tenantId, ticketId)
      ) as Array<{ id: string }> | undefined;

      // If we have optimistic messages, don't invalidate yet - wait for mutation to handle it
      const hasOptimisticMessages = currentMessages?.some((msg) =>
        msg.id?.startsWith('optimistic-')
      );

      if (!hasOptimisticMessages) {
        // ✨ PHANTOM MESSAGE FIX: Use structural sharing instead of manual invalidation
        // TanStack Query will automatically deduplicate via structural sharing
        this.queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.messages(this.tenantId, ticketId),
          refetchType: 'active',
        });

        // ✨ SMART REFETCH PREVENTION: Mark messages as recently updated
        this.preventRefetchFor(ticketId, 'message');
      }
      // If there are optimistic messages, the mutation's onSettled will handle the update
    } else {
      // For UPDATE/DELETE, always invalidate to refetch
      this.queryClient.invalidateQueries({
        queryKey: QueryKeys.TICKETS.messages(this.tenantId, ticketId),
        refetchType: 'active',
      });
    }

    // Update ticket's lastMessageAt in list caches if new message
    if (eventType === 'INSERT') {
      const listQueryKeys = [
        ['tickets', this.tenantId, 'list'],
        ['realtime-tickets', this.tenantId],
        QueryKeys.TICKETS.list(this.tenantId, { roleFilter: 'assigned' }),
        QueryKeys.TICKETS.list(this.tenantId, { roleFilter: 'all' }),
        QueryKeys.TICKETS.list(this.tenantId, { roleFilter: 'new' }),
      ];

      // CRITICAL FIX: Update all matching queries with new array reference
      listQueryKeys.forEach((queryKey) => {
        this.queryClient.setQueriesData(
          { queryKey: queryKey, exact: false },
          (old: Ticket[] | undefined) => {
            if (!old || !Array.isArray(old)) return old;

            // Update the ticket with new timestamps
            const updatedList = old.map((ticket) =>
              ticket.id === ticketId
                ? {
                    ...ticket,
                    lastMessageAt: new Date(),
                    updatedAt: new Date(),
                  }
                : ticket
            );

            // Force React to detect the change
            return [...updatedList];
          }
        );
      });
    }
  }

  /**
   * Handle user changes - simple invalidation
   */
  handleUserChange(payload: RealtimePostgresChangesPayload<UserRow>): void {
    const { eventType, new: newRow } = payload;

    // Type guard for user row data
    const hasId = (row: unknown): row is { id: string } =>
      typeof row === 'object' &&
      row !== null &&
      'id' in row &&
      typeof (row as { id: unknown }).id === 'string';

    const userId = hasId(newRow) ? newRow.id : undefined;

    console.log(`👤 User ${eventType}:`, userId);

    // Invalidate user queries
    this.queryClient.invalidateQueries({
      queryKey: QueryKeys.USERS.all(this.tenantId),
      refetchType: 'active',
    });

    // Invalidate tickets as user info might have changed
    this.queryClient.invalidateQueries({
      queryKey: ['tickets', this.tenantId],
      refetchType: 'active',
    });
  }

  /**
   * Check if two tickets are materially equal (ignoring updatedAt)
   */
  private areTicketsEqual(ticket1: Ticket, ticket2: Ticket): boolean {
    const compareFields: (keyof Ticket)[] = [
      'status',
      'priority',
      'assignedTo',
      'assignedToClerkId',
      'title',
      'description',
      'department',
      'resolvedAt',
      'closedAt',
    ];

    return compareFields.every((field) => ticket1[field] === ticket2[field]);
  }

  /**
   * ✨ NEW: Prevent refetch for recently updated items
   * This prevents unnecessary API calls immediately after real-time updates
   */
  private preventRefetchFor(itemId: string, type: 'ticket' | 'message'): void {
    const key = `__last_realtime_update__${type}_${this.tenantId}_${itemId}`;
    this.queryClient.setQueryData([key], Date.now());
  }

  /**
   * ✨ NEW: Check if item was recently updated by real-time
   * Used by query configurations to prevent immediate refetch
   */
  public wasRecentlyUpdated(
    itemId: string,
    type: 'ticket' | 'message',
    withinMs = 5000
  ): boolean {
    const key = `__last_realtime_update__${type}_${this.tenantId}_${itemId}`;
    const lastUpdate = this.queryClient.getQueryData([key]) as
      | number
      | undefined;

    if (!lastUpdate) return false;
    return Date.now() - lastUpdate < withinMs;
  }

  /**
   * Setup all event listeners on a channel
   */
  setupEventListeners(channel: RealtimeChannel): void {
    channel
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tickets',
          filter: `tenant_id=eq.${this.tenantId}`,
        },
        (payload: RealtimePostgresChangesPayload<TicketRow>) =>
          this.handleTicketChange(payload)
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'ticket_messages',
          filter: `tenant_id=eq.${this.tenantId}`,
        },
        (payload: RealtimePostgresChangesPayload<MessageRow>) =>
          this.handleMessageChange(payload)
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `tenant_id=eq.${this.tenantId}`,
        },
        (payload: RealtimePostgresChangesPayload<UserRow>) =>
          this.handleUserChange(payload)
      );
  }
}
