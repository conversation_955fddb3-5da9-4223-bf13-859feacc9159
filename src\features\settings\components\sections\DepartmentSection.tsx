'use client';

import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/features/shared/components/ui/button';
import { Card, CardContent } from '@/features/shared/components/ui/card';
import { Input } from '@/features/shared/components/ui/input';
import { Label } from '@/features/shared/components/ui/label';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/features/shared/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/features/shared/components/ui/select';

import { Plus, Trash2, FolderOpen } from 'lucide-react';
import { useTenant } from '@/features/tenant/store/use-tenant-store';
import { useTenantUuid } from '@/hooks/useTenantUuid';
import {
  useDepartments,
  type TenantDepartment,
} from '@/features/departments/hooks/useDepartments';
import {
  useCreateDepartment,
  useDeleteDepartment,
  useToggleDepartmentStatus,
} from '@/features/departments/hooks/useDepartmentMutations';
import { ConfirmationDialog } from '@/features/ticketing/components/ConfirmationDialog';
import { toast } from '@/features/shared/components/toast';
import { DepartmentSwitch } from '@/features/departments/components/DepartmentSwitch';

interface DepartmentFormData {
  name: string;
  color: string;
  dot_color: string;
  icon: string;
}

const departmentColors = [
  {
    label: 'Blue',
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    dot: 'bg-blue-500',
    value: 'blue',
  },
  {
    label: 'Green',
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    dot: 'bg-green-500',
    value: 'green',
  },
  {
    label: 'Orange',
    color:
      'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
    dot: 'bg-orange-500',
    value: 'orange',
  },
  {
    label: 'Purple',
    color:
      'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
    dot: 'bg-purple-500',
    value: 'purple',
  },
  {
    label: 'Pink',
    color: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300',
    dot: 'bg-pink-500',
    value: 'pink',
  },
  {
    label: 'Red',
    color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    dot: 'bg-red-500',
    value: 'red',
  },
  {
    label: 'Indigo',
    color:
      'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
    dot: 'bg-indigo-500',
    value: 'indigo',
  },
  {
    label: 'Teal',
    color: 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300',
    dot: 'bg-teal-500',
    value: 'teal',
  },
  {
    label: 'Cyan',
    color: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300',
    dot: 'bg-cyan-500',
    value: 'cyan',
  },
  {
    label: 'Emerald',
    color:
      'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300',
    dot: 'bg-emerald-500',
    value: 'emerald',
  },
  {
    label: 'Yellow',
    color:
      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    dot: 'bg-yellow-500',
    value: 'yellow',
  },
  {
    label: 'Slate',
    color: 'bg-slate-100 text-slate-800 dark:bg-slate-900 dark:text-slate-300',
    dot: 'bg-slate-500',
    value: 'slate',
  },
];

export function DepartmentSection() {
  const { tenantId } = useTenant();
  const tenantUuidQuery = useTenantUuid(tenantId || '');
  const tenantUuid = tenantUuidQuery.data;

  const { data: departments, isLoading } = useDepartments(tenantUuid || null);
  const createDepartment = useCreateDepartment();
  const deleteDepartment = useDeleteDepartment();
  const toggleDepartmentStatus = useToggleDepartmentStatus();

  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    department: TenantDepartment | null;
  }>({ isOpen: false, department: null });

  // Track which department is currently being toggled or deleted
  const [togglingDepartmentId, setTogglingDepartmentId] = useState<
    string | null
  >(null);
  const [deletingDepartmentId, setDeletingDepartmentId] = useState<
    string | null
  >(null);

  // Get colors that are already in use by departments
  const usedColors = useMemo(
    () => new Set(departments?.map((dept) => dept.color) || []),
    [departments]
  );

  // Get the first available color for new departments
  const firstAvailableColor =
    departmentColors.find((color) => !usedColors.has(color.color)) ||
    departmentColors[0];

  const [formData, setFormData] = useState<DepartmentFormData>(() => ({
    name: '',
    color:
      firstAvailableColor?.color ||
      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    dot_color: firstAvailableColor?.dot || 'bg-blue-500',
    icon: 'folder',
  }));

  // Handle color selection from dropdown
  const handleColorChange = (colorValue: string) => {
    const selectedColor = departmentColors.find((c) => c.value === colorValue);
    if (selectedColor) {
      setFormData((prev) => ({
        ...prev,
        color: selectedColor.color,
        dot_color: selectedColor.dot,
      }));
    }
  };

  // Get available colors for dropdown (not used by other departments)
  const availableColors = useMemo(
    () => departmentColors.filter((color) => !usedColors.has(color.color)),
    [usedColors]
  );

  // Get current selected color value - ensure it matches the form data
  const selectedColorValue =
    departmentColors.find((c) => c.color === formData.color)?.value ||
    (availableColors.length > 0 ? availableColors[0]?.value || 'blue' : 'blue');

  // Synchronize form data when create dialog opens or available colors change
  useEffect(() => {
    if (isCreateOpen) {
      // Reset form with the current first available color
      const currentFirstAvailable = availableColors[0];
      if (currentFirstAvailable) {
        setFormData({
          name: '',
          color: currentFirstAvailable.color,
          dot_color: currentFirstAvailable.dot,
          icon: 'folder',
        });
      }
    }
  }, [isCreateOpen, availableColors]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if color is already used by another department
    if (usedColors.has(formData.color)) {
      toast.error('Color Already in Use', {
        description:
          'This color is already used by another department. Please select a different color.',
      });
      return;
    }

    try {
      await createDepartment.mutateAsync(formData);
      setIsCreateOpen(false);

      // Reset form with first available color
      setFormData({
        name: '',
        color:
          firstAvailableColor?.color ||
          'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
        dot_color: firstAvailableColor?.dot || 'bg-blue-500',
        icon: 'folder',
      });
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error('Error submitting department form:', error);
    }
  };

  const handleDelete = (department: TenantDepartment) => {
    setDeleteConfirmation({
      isOpen: true,
      department,
    });
  };

  const confirmDelete = async () => {
    if (!deleteConfirmation.department) return;

    const departmentId = deleteConfirmation.department.id;

    try {
      setDeletingDepartmentId(departmentId);
      await deleteDepartment.mutateAsync(departmentId);
      setDeleteConfirmation({ isOpen: false, department: null });
    } catch (error) {
      // Error handling is done in the mutation hook
      console.warn('Department deletion failed:', error);
    } finally {
      setDeletingDepartmentId(null);
    }
  };

  const handleToggleStatus = async (department: TenantDepartment) => {
    try {
      setTogglingDepartmentId(department.id);
      await toggleDepartmentStatus.mutateAsync({
        id: department.id,
        is_active: !department.is_active,
      });
    } finally {
      setTogglingDepartmentId(null);
    }
  };

  if (isLoading) {
    return (
      <div className='space-y-6'>
        <div>
          <h3 className='text-lg font-medium'>Departments</h3>
          <p className='text-sm text-muted-foreground'>
            Loading departments...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Title and Add Button - Outside the card */}
      <div className='flex items-center justify-between'>
        <div>
          <h3 className='text-lg font-medium'>Departments</h3>
          <p className='text-sm text-muted-foreground'>
            Manage your organization's departments for ticket categorization
          </p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className='h-4 w-4 mr-2' />
              Add Department
            </Button>
          </DialogTrigger>
          <DialogContent>
            <form onSubmit={handleSubmit}>
              <DialogHeader>
                <DialogTitle>Create Department</DialogTitle>
                <DialogDescription>
                  Add a new department to organize your tickets
                </DialogDescription>
              </DialogHeader>

              <div className='space-y-4 py-4'>
                <div className='space-y-2'>
                  <Label htmlFor='name'>Department Name</Label>
                  <Input
                    id='name'
                    value={formData.name}
                    onChange={(e) =>
                      setFormData((prev) => ({ ...prev, name: e.target.value }))
                    }
                    placeholder='e.g., Customer Support'
                    required
                  />
                </div>

                <div className='space-y-2'>
                  <Label>Color Theme</Label>
                  <Select
                    value={selectedColorValue}
                    onValueChange={handleColorChange}
                  >
                    <SelectTrigger className='w-full'>
                      <SelectValue>
                        <div className='flex items-center gap-2'>
                          <div
                            className={`w-1.5 h-1.5 rounded-full ${departmentColors.find((c) => c.value === selectedColorValue)?.dot}`}
                          />
                          {
                            departmentColors.find(
                              (c) => c.value === selectedColorValue
                            )?.label
                          }
                        </div>
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {availableColors.map((color) => (
                        <SelectItem key={color.value} value={color.value}>
                          <div className='flex items-center gap-2'>
                            <div
                              className={`w-1.5 h-1.5 rounded-full ${color.dot}`}
                            />
                            {color.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <DialogFooter>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setIsCreateOpen(false)}
                >
                  Cancel
                </Button>
                <Button type='submit' disabled={createDepartment.isPending}>
                  {createDepartment.isPending
                    ? 'Creating...'
                    : 'Create Department'}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Department List - Inside the card */}
      <Card>
        <CardContent className='p-6'>
          <div className='space-y-3'>
            {departments?.map((department) => (
              <div
                key={department.id}
                className='flex items-center justify-between p-3 rounded-lg border'
              >
                <div className='flex items-center gap-3'>
                  <FolderOpen className='h-4 w-4 text-muted-foreground' />
                  <div className='text-sm font-medium'>{department.name}</div>
                </div>

                <div className='flex items-center gap-3'>
                  {/* Active/Inactive Switch */}
                  <div className='flex items-center gap-2'>
                    <DepartmentSwitch
                      checked={department.is_active}
                      onCheckedChange={() => handleToggleStatus(department)}
                      disabled={togglingDepartmentId === department.id}
                      departmentColor={department.dot_color}
                    />
                    <span className='text-xs font-medium text-muted-foreground'>
                      {department.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>

                  {/* Delete button */}
                  <div className='flex items-center gap-1'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() => handleDelete(department)}
                      disabled={
                        deletingDepartmentId === department.id ||
                        deleteDepartment.isPending
                      }
                    >
                      {deletingDepartmentId === department.id ? (
                        <div className='h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent' />
                      ) : (
                        <Trash2 className='h-3 w-3' />
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            ))}

            {departments?.length === 0 && (
              <div className='text-center py-8 text-muted-foreground'>
                <div className='w-12 h-12 mx-auto mb-4 opacity-50 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center'>
                  <FolderOpen className='h-6 w-6' />
                </div>
                <p>
                  No departments found. Create your first department to get
                  started.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={deleteConfirmation.isOpen}
        onOpenChange={(open) =>
          !deletingDepartmentId &&
          setDeleteConfirmation({ isOpen: open, department: null })
        }
        title='Delete Department'
        description={
          deleteConfirmation.department
            ? `Are you sure you want to delete "${deleteConfirmation.department.name}"? This action cannot be undone and may affect existing tickets assigned to this department.`
            : ''
        }
        onConfirm={confirmDelete}
        variant='destructive'
        isLoading={!!deletingDepartmentId}
        confirmText='Delete Department'
      />
    </div>
  );
}
