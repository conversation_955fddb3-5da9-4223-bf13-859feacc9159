{"permissions": {"allow": ["<PERSON><PERSON>(powershell:*)", "<PERSON><PERSON>(claude mcp get:*)", "mcp__playwright__browser_navigate", "mcp__playwright__browser_go_back", "mcp__playwright__browser_go_forward", "mcp__playwright__browser_tab_list", "mcp__playwright__browser_tab_new", "mcp__playwright__browser_tab_select", "mcp__playwright__browser_tab_close", "mcp__playwright__browser_click", "mcp__playwright__browser_type", "mcp__playwright__browser_hover", "mcp__playwright__browser_press_key", "mcp__playwright__browser_select_option", "mcp__playwright__browser_file_upload", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_drag", "mcp__playwright__browser_resize", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_network_requests", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_close", "mcp__playwright__browser_install", "mcp__playwright__playwright_navigate", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_get_visible_text", "mcp__playwright__playwright_get_visible_html", "mcp__playwright__playwright_console_logs", "mcp__playwright__playwright_evaluate", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_click", "Bash(npm install:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm run lint)", "Bash(npm run type-check:*)", "Bash(find:*)", "Bash(npm run typecheck:*)", "mcp__taskmanager__request_planning", "mcp__taskmanager__add_tasks_to_request", "mcp__taskmanager__get_next_task", "mcp__taskmanager__mark_task_done", "mcp__taskmanager__approve_task_completion", "mcp__taskmanager__approve_request_completion", "mcp__taskmanager__list_requests", "mcp__taskmanager__open_task_details", "mcp__taskmanager__update_task", "mcp__taskmanager__delete_task", "WebFetch(domain:clerk.com)", "mcp__sequential-thinking__sequentialthinking", "Bash(npm run lint:*)", "<PERSON><PERSON>(npx prettier:*)", "<PERSON><PERSON>(sed:*)", "Bash(npx tsc:*)", "Bash(export:*)", "mcp__playwright__browser_wait_for", "<PERSON><PERSON>(tasklist:*)", "<PERSON><PERSON>(del analyze-db.js)", "Bash(npx supabase:*)", "Bash(winget install:*)", "Bash(npx:*)", "Bash(set SUPABASE_ACCESS_TOKEN=********************************************)", "WebFetch(domain:xprwqadnmauhpschgkwk.supabase.co)", "<PERSON><PERSON>(curl:*)", "mcp__supabase__get_project_url", "mcp__supabase__get_anon_key", "mcp__supabase__list_tables", "mcp__supabase__list_migrations", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(grep:*)", "mcp__playwright__browser_navigate_forward", "mcp__playwright__browser_navigate_back", "mcp__playwright__mcp__playwright__browser_wait_for_selector", "mcp__playwright__mcp__playwright__browser_query_selector_all", "mcp__playwright__mcp__playwright__browser_get_title", "Bash(timeout 5 tail -50 .next/server/app/tickets/page.js)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "mcp__playwright__mcp__playwright__browser_get_text_content", "mcp__playwright__mcp__playwright__browser_evaluate", "mcp__supabase__execute_sql", "mcp__playwright__mcp__playwright__browser_get_html", "mcp__playwright__mcp__playwright__browser_wait_for_function", "mcp__playwright__mcp__playwright__browser_get_responses", "mcp__playwright__mcp__playwright__browser_get_requests", "mcp__playwright__mcp__playwright__browser_query_selector", "mcp__playwright__mcp__playwright__browser_eval_on_selector", "mcp__browser-tool__browser_navigate", "mcp__browser-tool__browser_snapshot", "mcp__browser-tool__browser_click", "mcp__browser-tool__browser_type", "mcp__browser-tool__mcp__playwright__browser_wait_for_selector", "mcp__browser-tool__mcp__playwright__browser_get_html", "mcp__browser-tool__browser_console_messages", "mcp__browser-tool__mcp__playwright__browser_evaluate", "mcp__online-research-tool__answer_query_websearch", "mcp__online-research-tool__explain_topic_with_docs", "mcp__browser-tool__browser_wait_for", "mcp__browser-tool__mcp__playwright__browser_query_selector_all", "mcp__browser-tool__browser_navigate_forward", "mcp__browser-tool__browser_network_requests", "mcp__browser-tool__mcp__playwright__browser_get_requests", "mcp__browser-tool__browser_close", "Bash(npm run format:*)", "mcp__browser-tool__mcp__playwright__browser_get_url", "mcp__browser-tool__mcp__playwright__browser_query_selector", "mcp__browser-tool__mcp__playwright__browser_wait_for_load_state", "<PERSON><PERSON>(dir:*)", "mcp__browser-tool__mcp__playwright__browser_wait_for_event", "mcp__browser-tool__mcp__playwright__browser_wait_for_function", "mcp__browser-tool__mcp__playwright__browser_get_accessibility_tree", "mcp__browser-tool__mcp__playwright__browser_eval_on_selector", "mcp__browser-tool__mcp__playwright__browser_get_text_content"], "deny": []}}