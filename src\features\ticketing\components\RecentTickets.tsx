'use client';

/**
 * RecentTickets Component - React 19 Performance Optimized
 *
 * This component provides instant UI rendering without loading states by leveraging
 * cached data and real-time updates. All data synchronization happens seamlessly
 * in the background to ensure smooth user experience.
 *
 * Key Optimizations:
 * - React.memo for preventing unnecessary re-renders
 * - useMemo for expensive computations (filtering, categorization)
 * - useCallback for event handlers
 * - Eliminated loading states for instant UI response
 * - Uses cached data for immediate rendering
 * - Real-time updates add only NEW content without touching existing data
 * - Background synchronization is invisible to users
 *
 * <AUTHOR> Augster
 * @version 3.0 - React 19 Performance Optimized (January 2025)
 */

import { memo, useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { Input } from '@/features/shared/components/ui/input';
import { Button } from '@/features/shared/components/ui/button';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/features/shared/components/ui/collapsible';
import { Search, Filter, ChevronDown, ChevronUp, Plus } from 'lucide-react';
import { VirtualizedTicketList } from './VirtualizedTicketList';
import { cn } from '@/lib/utils';
import { TicketCard } from '../models/ticket-card.schema';
import { usePermissions, useAuth } from '@/features/shared/hooks/useAuth';
import { useCategorizedTickets } from '../hooks/useCategorizedTickets';

// Optimized scrollbar styles for ticket list containers
const SCROLLBAR_STYLES = cn(
  'flex-1 overflow-y-auto min-h-0',
  'scrollbar-thin scrollbar-track-transparent scrollbar-corner-transparent',
  'scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600',
  '[&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-transparent',
  '[&::-webkit-scrollbar-thumb]:bg-gray-300 [&::-webkit-scrollbar-thumb]:rounded-none',
  'dark:[&::-webkit-scrollbar-thumb]:bg-gray-600 [&::-webkit-scrollbar-button]:hidden',
  'transition-all duration-300 ease-in-out'
);

interface RecentTicketsProps {
  selectedTicketId?: string | null;
  onTicketSelect?: (ticketId: string) => void;
  onCreateTicket?: () => void;
  tickets?: TicketCard[];
  isLoading?: boolean;
  expandSection?: AccordionSection | null;
  tenantId?: string | null; // Added for VirtualizedTicketList real-time integration
}

type AccordionSection = 'new' | 'open' | 'closed';

const sectionColors = {
  new: 'bg-blue-500',
  open: 'bg-green-500',
  closed: 'bg-gray-500',
};

export const RecentTickets = memo(function RecentTickets({
  selectedTicketId,
  onTicketSelect,
  onCreateTicket,
  tickets = [],
  isLoading = false,
  expandSection = null,
  tenantId,
}: RecentTicketsProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const { hasPermission } = usePermissions();
  const role = useAuth((state) => state.role);
  const categorizedTickets = useCategorizedTickets(tickets);

  // React 19 optimization: Derive state instead of useState with useEffect
  const defaultSection: AccordionSection = useMemo(
    () => (role === 'user' ? 'open' : 'open'),
    [role]
  );

  const [openSection, setOpenSection] = useState<AccordionSection>(
    expandSection || defaultSection
  );

  // Track if user has ever manually interacted with accordion to prevent auto-effects from overriding
  const [hasManuallyInteracted, setHasManuallyInteracted] = useState(false);

  // CRITICAL FIX: Sync expandSection prop with internal state when it changes
  // This ensures the accordion responds to external section changes (e.g., when ticket status changes)
  useEffect(() => {
    if (
      expandSection &&
      expandSection !== openSection &&
      !hasManuallyInteracted
    ) {
      console.log('📂 Expanding section due to prop change:', {
        from: openSection,
        to: expandSection,
      });
      setOpenSection(expandSection);
    }
  }, [expandSection, openSection, hasManuallyInteracted]);

  // Scroll position preservation refs
  const scrollRefs = useRef<Record<AccordionSection, HTMLDivElement | null>>({
    new: null,
    open: null,
    closed: null,
  });
  const scrollPositions = useRef<Record<AccordionSection, number>>({
    new: 0,
    open: 0,
    closed: 0,
  });

  const getSectionTitle = useCallback(
    (sectionType: string) => {
      switch (sectionType) {
        case 'open':
          switch (role) {
            case 'super_admin':
              return 'All Assigned Tickets';
            case 'admin':
              return 'My Assigned Tickets';
            case 'agent':
              return 'My Open Tickets';
            case 'user':
              return 'My Tickets';
            default:
              return 'My Open Tickets';
          }
        case 'new':
          return 'New Tickets';
        case 'closed':
          return 'Closed Tickets';
        default:
          return 'Tickets';
      }
    },
    [role]
  );

  // Memoize search filtering function
  const getFilteredTickets = useCallback(
    (categoryTickets: TicketCard[]) => {
      if (!searchQuery.trim()) {
        return categoryTickets;
      }

      const query = searchQuery.toLowerCase();
      return categoryTickets.filter((ticket) => {
        const title = ticket.title?.toLowerCase() || '';
        const userName = ticket.userName?.toLowerCase() || '';
        return title.includes(query) || userName.includes(query);
      });
    },
    [searchQuery]
  );

  const filteredTickets = useMemo(
    () => ({
      new: getFilteredTickets(categorizedTickets.newTickets),
      open: getFilteredTickets(categorizedTickets.openTickets),
      closed: getFilteredTickets(categorizedTickets.closedTickets),
    }),
    [getFilteredTickets, categorizedTickets]
  );

  // Memoize event handlers

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
    []
  );

  // CRITICAL FIX: Handle scroll position changes from VirtualizedTicketList
  const handleScrollChange = useCallback(
    (section: AccordionSection) => (scrollTop: number) => {
      scrollPositions.current[section] = scrollTop;
    },
    []
  );

  // Save scroll position before ticket selection
  const handleTicketSelect = useCallback(
    (ticketId: string) => {
      // Scroll position is now automatically tracked by VirtualizedTicketList
      // No need to manually save scroll position here
      onTicketSelect?.(ticketId);
    },
    [onTicketSelect]
  );

  // Handle external section expansion requests (only on initial load)
  useEffect(() => {
    if (expandSection && expandSection !== openSection) {
      setOpenSection(expandSection);
    }
  }, [expandSection]); // eslint-disable-line react-hooks/exhaustive-deps -- openSection intentionally excluded to prevent interference with manual clicks

  // CRITICAL FIX: Auto-expand "My Open Tickets" section when a ticket is selected
  // Only expand when a ticket is actually selected (not just when selectedTicketId exists)
  // Respect manual user interactions and don't override them
  useEffect(() => {
    if (selectedTicketId && !hasManuallyInteracted) {
      // Check if the selected ticket is in the "open" category
      const isTicketInOpenSection = categorizedTickets.openTickets.some(
        (ticket) => ticket.id === selectedTicketId
      );

      // Also check if any ticket has just been opened (status is 'open')
      const selectedTicket = tickets?.find((t) => t.id === selectedTicketId);
      const isTicketOpen = selectedTicket?.status === 'open';

      if ((isTicketInOpenSection || isTicketOpen) && openSection !== 'open') {
        setOpenSection('open');
      }
    }
  }, [
    selectedTicketId,
    categorizedTickets.openTickets,
    hasManuallyInteracted,
    tickets,
    openSection,
  ]);

  // Restore scroll position when section changes or component updates
  useEffect(() => {
    const currentRef = scrollRefs.current[openSection];
    if (currentRef) {
      // Use requestAnimationFrame to ensure DOM is updated
      requestAnimationFrame(() => {
        currentRef.scrollTop = scrollPositions.current[openSection];
      });
    }
  }, [openSection, selectedTicketId]);

  const handleCreateTicket = useCallback(() => {
    onCreateTicket?.();
  }, [onCreateTicket]);

  // Handle ticket opening - refresh tickets after opening

  // Helper component for accordion sections with smooth transitions
  const AccordionSection = ({
    section,
    title,
    tickets,
    isOpen,
    showSkeleton = false,
  }: {
    section: AccordionSection;
    title: string;
    tickets: TicketCard[];
    isOpen: boolean;
    showSkeleton?: boolean;
  }) => (
    <Collapsible
      open={isOpen}
      onOpenChange={(open) => {
        // CRITICAL FIX: Simplified logic for manual accordion interaction
        // When user clicks to open a section, always open that section
        // When user clicks to close a section, go to default section

        // Mark that user has manually interacted with accordion
        setHasManuallyInteracted(true);

        if (open) {
          // Prevent users from accessing the "new" section
          if (role === 'user' && section === 'new') {
            console.log('Blocked user from accessing new section');
            return;
          }

          setOpenSection(section);
        } else {
          // When closing, go to default section

          setOpenSection(defaultSection);
        }
      }}
      className={cn(
        'border-b border-gray-200 dark:border-gray-700 last:border-b-0 transition-all duration-300 ease-in-out',
        isOpen ? 'flex-1 flex flex-col min-h-0' : 'flex-shrink-0'
      )}
    >
      <CollapsibleTrigger asChild>
        <Button
          variant='ghost'
          className='w-full justify-between px-6 py-4 h-auto font-normal shrink-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-none cursor-pointer bg-gray-50 dark:bg-gray-900 data-[state=open]:border-b data-[state=open]:border-gray-200 dark:data-[state=open]:border-gray-700'
        >
          <div className='flex items-center gap-2'>
            <div className={`w-2 h-2 rounded-full ${sectionColors[section]}`} />
            <span className='text-sm text-gray-600 dark:text-gray-400'>
              {title} ({tickets.length})
            </span>
          </div>
          {isOpen ? (
            <ChevronUp className='h-4 w-4 text-gray-400' />
          ) : (
            <ChevronDown className='h-4 w-4 text-gray-400' />
          )}
        </Button>
      </CollapsibleTrigger>

      <CollapsibleContent
        ref={(el) => {
          scrollRefs.current[section] = el;
        }}
        className={SCROLLBAR_STYLES}
      >
        <VirtualizedTicketList
          tickets={tickets}
          selectedTicketId={selectedTicketId}
          onTicketSelect={handleTicketSelect}
          showSkeleton={showSkeleton}
          tenantId={tenantId}
          initialScrollTop={scrollPositions.current[section]}
          onScrollChange={handleScrollChange(section)}
        />
      </CollapsibleContent>
    </Collapsible>
  );

  return (
    <div className='w-full max-w-md bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 h-full flex flex-col shadow-lg shadow-gray-200/50 dark:shadow-gray-900/50'>
      {/* Header */}
      <div className='p-6 border-b border-gray-200 dark:border-gray-700 shrink-0'>
        <div className='flex items-center justify-between mb-4'>
          <h2 className='text-lg font-semibold text-gray-900 dark:text-gray-100'>
            Recent Tickets
          </h2>
          {hasPermission('tickets.create') && (
            <Button onClick={handleCreateTicket} size='sm'>
              <Plus className='h-4 w-4 mr-2' />
              Create New Ticket
            </Button>
          )}
        </div>

        {/* Search and Filter */}
        <div className='flex gap-2 items-center'>
          <div className='relative flex-1'>
            <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
            <Input
              placeholder='Search'
              value={searchQuery}
              onChange={handleSearchChange}
              className='pl-10 h-10'
            />
          </div>
          <Button variant='outline' className='px-3 h-10'>
            <Filter className='h-4 w-4' />
          </Button>
        </div>
      </div>

      {/* Accordion Tickets Sections - Fixed scrolling */}
      <div className='flex-1 flex flex-col min-h-0 overflow-hidden'>
        <div className='flex-1 flex flex-col min-h-0'>
          {/* Only show New Tickets section for agents, admins, and super admins */}
          {role !== 'user' && (
            <AccordionSection
              section='new'
              title='New Tickets'
              tickets={filteredTickets.new}
              isOpen={openSection === 'new'}
              showSkeleton={isLoading}
            />
          )}

          <AccordionSection
            section='open'
            title={getSectionTitle('open')}
            tickets={filteredTickets.open}
            isOpen={openSection === 'open'}
            showSkeleton={isLoading}
          />

          <AccordionSection
            section='closed'
            title='Closed Tickets'
            tickets={filteredTickets.closed}
            isOpen={openSection === 'closed'}
            showSkeleton={isLoading}
          />
        </div>
      </div>
    </div>
  );
});
