/**
 * Message Deduplication - 2025 Simplified <PERSON><PERSON>
 *
 * Simple and efficient duplicate detection for real-time messages.
 * Replaces 126 lines of complex logic with 40 lines.
 */

// Message interface for compatibility with API responses and TicketMessage
export interface Message {
  id: string;
  content: string;
  createdAt: Date | string;
  authorId: string;
  authorName: string;
  authorEmail?: string | undefined;
  authorAvatar?: string | undefined;
  ticketId: string;
  attachments: Array<{
    id: string;
    name: string;
    type: string;
    size: number;
    url: string;
    uploadedAt: Date;
  }>;
  // API compatibility fields
  created_at?: string;
  author_id?: string;
}

/**
 * Check if two messages are duplicates
 * Enhanced to handle the sender duplicate issue reported by user
 */
export function isDuplicateMessage(
  existingMessage: Message,
  incomingMessage: Message
): boolean {
  // Same ID = definite duplicate
  if (existingMessage.id === incomingMessage.id) {
    return true;
  }

  // Different content = not duplicate
  if (existingMessage.content !== incomingMessage.content) {
    return false;
  }

  // Handle API field variations (snake_case vs camelCase)
  const existingAuthor = existingMessage.authorId || existingMessage.author_id;
  const incomingAuthor = incomingMessage.authorId || incomingMessage.author_id;

  // Get timestamps handling both formats
  const existingCreated =
    existingMessage.createdAt || existingMessage.created_at;
  const incomingCreated =
    incomingMessage.createdAt || incomingMessage.created_at;

  if (!existingCreated || !incomingCreated) {
    return false;
  }

  const existingTime = new Date(existingCreated).getTime();
  const incomingTime = new Date(incomingCreated).getTime();
  const timeDiff = Math.abs(existingTime - incomingTime);

  // For same author + same content within 30 seconds = duplicate
  // This catches the "sender sees duplicate" issue
  if (existingAuthor && incomingAuthor && existingAuthor === incomingAuthor) {
    return timeDiff < 30000; // 30 seconds for same author
  }

  // For different authors or unknown authors, use tighter window
  return timeDiff < 5000; // 5 seconds
}

/**
 * Deduplicate an array of messages
 * Enhanced to handle the sender duplicate issue
 */
export function deduplicateMessages(messages: Message[]): Message[] {
  if (!messages || messages.length === 0) {
    return [];
  }

  const deduplicated: Message[] = [];

  for (const message of messages) {
    // Check if this message is a duplicate of any already processed
    const isDuplicate = deduplicated.some((existing) =>
      isDuplicateMessage(existing, message)
    );

    if (!isDuplicate) {
      deduplicated.push(message);
    } else {
      // If duplicate found, prefer non-optimistic IDs
      const existingIndex = deduplicated.findIndex((existing) =>
        isDuplicateMessage(existing, message)
      );

      if (existingIndex !== -1) {
        const existing = deduplicated[existingIndex];
        // Replace optimistic with real message
        if (
          existing &&
          existing.id.startsWith('optimistic-') &&
          !message.id.startsWith('optimistic-')
        ) {
          deduplicated[existingIndex] = message;
        }
      }
    }
  }

  // Sort by creation time to maintain chronological order
  return deduplicated.sort((a, b) => {
    const aTime = new Date(a.createdAt || a.created_at || 0).getTime();
    const bTime = new Date(b.createdAt || b.created_at || 0).getTime();
    return aTime - bTime;
  });
}

/**
 * Create a deduplication key for a message
 * Used for quick lookups in real-time scenarios
 */
export function getMessageDeduplicationKey(message: Message): string {
  const author = message.authorId || message.author_id || 'unknown';
  const created = message.createdAt || message.created_at || new Date();
  const timestamp = Math.floor(new Date(created).getTime() / 30000); // 30-second buckets

  return `${author}-${message.content.substring(0, 50)}-${timestamp}`;
}
