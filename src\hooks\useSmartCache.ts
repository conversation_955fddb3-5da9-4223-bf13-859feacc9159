/**
 * Smart Cache Hook - 2025 Pattern
 *
 * Simple hook that manages cache persistence lifecycle.
 * Loads on mount, saves periodically and on unmount.
 */

import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { DexieCacheManager } from '@/lib/cache/dexie-cache-manager';
import { useAuth } from '@/features/shared/hooks/useAuth';

export function useSmartCache() {
  const queryClient = useQueryClient();
  const { tenantId } = useAuth();
  const cacheManagerRef = useRef<DexieCacheManager | null>(null);
  const saveIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!tenantId) return;

    // Initialize cache manager
    const cacheManager = new DexieCacheManager(queryClient);
    cacheManagerRef.current = cacheManager;

    // Restore cache on mount (this happens instantly)
    console.log('🚀 Restoring tickets from IndexedDB cache...');
    cacheManager.restoreCache(tenantId);

    // Save cache before page unload
    const handleBeforeUnload = () => {
      cacheManager.persistCache(tenantId);
    };

    // Save cache when page becomes hidden (mobile support)
    const handleVisibilityChange = () => {
      if (document.hidden) {
        cacheManager.persistCache(tenantId);
      }
    };

    // Save cache periodically (every 30 seconds)
    saveIntervalRef.current = setInterval(() => {
      cacheManager.persistCache(tenantId);
    }, 30000);

    // Prune old data occasionally (every 5 minutes)
    const pruneInterval = setInterval(
      () => {
        cacheManager.pruneCache(tenantId);
      },
      5 * 60 * 1000
    );

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      // Save cache on unmount
      cacheManager.persistCache(tenantId);

      // Cleanup
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (saveIntervalRef.current) {
        clearInterval(saveIntervalRef.current);
      }
      clearInterval(pruneInterval);
    };
  }, [tenantId, queryClient]);

  // No need to return anything - it just works
}
