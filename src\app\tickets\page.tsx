'use client';

/**
 * FORCE REBUILD v3 - React Query Optimized Tickets Page - 2025 Implementation
 * Uses React Query for all server state management with minimal code footprint
 * DEBUGGING: This component should render and show console logs!
 */

import { useEffect, useCallback } from 'react';
import dynamic from 'next/dynamic';
import { AppLayout } from '@/features/shared/components/AppLayout';
import { RecentTickets } from '@/features/ticketing/components/RecentTickets';
import { useAuth, usePermissions } from '@/features/shared/hooks/useAuth';
import { useAuth as useClerkAuth } from '@clerk/nextjs';
import { useSettingsSync } from '@/features/settings/hooks/useSettingsSync';
import { VisitorInformation } from '@/features/visitor/components/VisitorInformation';
import { useRealtimeTicket } from '@/hooks/useTickets';
import {
  useTicketingUISelectors,
  useTicketingUIActions,
} from '@/features/ticketing/store/use-ticketing-store';
import { useRealtimeTickets } from '@/hooks/realtime/useRealtimeTickets';
import { useSmartCacheRefresh } from '@/hooks/realtime/useSmartCacheRefresh';
import { useQueryClient } from '@tanstack/react-query';
import { QueryKeys } from '@/lib/query-keys';
import { useTenantUuid } from '@/hooks/useTenantUuid';

import { useTicketWorkflow } from '@/features/ticketing/hooks/useTicketWorkflow';
import type { Ticket } from '@/features/ticketing/models/ticket.schema';
import type { TicketCard } from '@/features/ticketing/models/ticket-card.schema';
import { ReplyDraftConfirmDialog } from '@/features/ticketing/components/ReplyDraftConfirmDialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/features/shared/components/ui/dialog';
import { Button } from '@/features/shared/components/ui/button';
import Link from 'next/link';

// Dynamic imports for better performance
const TicketDetail = dynamic(
  () =>
    import('@/features/ticketing/components/TicketDetail').then((mod) => ({
      default: mod.TicketDetail,
    })),
  {
    ssr: false,
  }
);

const CreateTicketForm = dynamic(
  () =>
    import('@/features/ticketing/components/CreateTicketForm').then((mod) => ({
      default: mod.CreateTicketForm,
    })),
  {
    ssr: false,
  }
);

function TicketsPageContent() {
  const { user, isLoaded, isSignedIn, role, tenantId } = useAuth();
  const { hasPermission } = usePermissions();
  const queryClient = useQueryClient();

  // PROPER FIX: Use official Clerk patterns - isLoaded and isSignedIn are sufficient
  const clerkAuth = useClerkAuth();

  // Based on official Clerk documentation - no need to manually check session status
  const isSessionActive = isLoaded && isSignedIn;
  const canRender = isLoaded && isSignedIn; // Allow rendering when signed in

  // DEBUG: Log session status with more detail
  console.log('🔐 Session debug:', {
    isLoaded,
    isSignedIn,
    sessionStatus: clerkAuth.sessionId ? 'active' : 'inactive',
    sessionExists: !!clerkAuth.sessionId,
    sessionId: clerkAuth.sessionId,
    isSessionActive,
    canRender,
    user: !!user,
    tenantId,
    queryEnabled: !!tenantId && isSessionActive,
  });
  const domainInfo = { isLocalhost: true };

  // Initialize settings sync
  useSettingsSync();

  // CRITICAL FIX: Pre-resolve tenant UUID to prevent delays in TicketDetail
  // This ensures the UUID is cached before any ticket selection
  const tenantUuidQuery = useTenantUuid(tenantId || '');
  const tenantUuid = tenantUuidQuery.data;

  // Smart background cache validation on page refresh
  useSmartCacheRefresh(tenantId || '');

  // UI state management (Zustand for UI-only state)
  const selectedTicketId = useTicketingUISelectors.useSelectedTicketId();
  const setSelectedTicketId = useTicketingUIActions.useSetSelectedTicketId();
  const setCurrentTenantId = useTicketingUIActions.useSetCurrentTenantId();

  // CRITICAL FIX: Synchronize tenant ID with UI store for TicketDetail component
  useEffect(() => {
    if (tenantId && isLoaded && isSignedIn) {
      setCurrentTenantId(tenantId);
    }
  }, [tenantId, isLoaded, isSignedIn, setCurrentTenantId]);

  // Create role-based filter context for React Query
  const filterContext = {
    tenantId: tenantId || '',
    role: role || 'agent',
    userId: user?.id || '',
    email: user?.emailAddresses?.[0]?.emailAddress || '',
  };

  // Realtime tickets with proper auth checks and error handling
  const ticketsQuery = useRealtimeTickets(filterContext, {
    enabled: !!tenantId && isSessionActive, // Uses proper Clerk session status
  });

  // AUTHENTICATION FIX: Log when session is active but no tickets
  useEffect(() => {
    if (
      isSessionActive &&
      tenantId &&
      !ticketsQuery.isLoading &&
      !ticketsQuery.isError &&
      (!ticketsQuery.data || ticketsQuery.data.length === 0)
    ) {
      console.log('🔐 Session active but no tickets, checking query state:', {
        isLoaded,
        isSignedIn,
        sessionStatus: clerkAuth.sessionId ? 'active' : 'inactive',
        sessionId: clerkAuth.sessionId,
        isSessionActive,
        tenantId,
        queryEnabled: !!tenantId && isSessionActive,
        queryState: {
          isLoading: ticketsQuery.isLoading,
          isError: ticketsQuery.isError,
          isStale: ticketsQuery.isStale,
          dataLength: ticketsQuery.data?.length || 0,
        },
      });

      // Trigger refetch if conditions are met
      if (ticketsQuery.isStale && !ticketsQuery.isFetching) {
        console.log('🔄 Triggering manual refetch');
        ticketsQuery.refetch();
      }
    }
  }, [
    isLoaded,
    isSignedIn,
    clerkAuth.sessionId,
    isSessionActive,
    tenantId,
    ticketsQuery,
  ]);

  // Extract data from queries with proper loading state management
  // RACE CONDITION FIX: Handle authentication transition errors gracefully
  // CRITICAL FIX: Include optimistic tickets from cache
  const apiTickets = (!ticketsQuery.isError && ticketsQuery.data) || [];

  // LAZY LOADING: Use cached ticket cards from React Query
  // These are lightweight card representations, not full tickets
  const queryKey = QueryKeys.TICKETS.list(tenantId || '', {
    roleFilter: role === 'agent' ? 'assigned' : 'all',
  });
  const cachedTickets = queryClient.getQueryData<TicketCard[]>(queryKey) || [];

  // Use the cached card data if it exists
  // Otherwise fall back to API data
  const tickets: TicketCard[] =
    cachedTickets.length > 0 ? cachedTickets : apiTickets;

  // Debug logging
  const optimisticCount = tickets.filter(
    (ticket: TicketCard) =>
      ticket.id?.startsWith('optimistic-') || ticket.id?.startsWith('temp-')
  ).length;

  if (optimisticCount > 0 || cachedTickets.length !== apiTickets.length) {
    console.log('🔍 Ticket cache analysis:', {
      tenantId,
      queryKey,
      cachedTicketsCount: cachedTickets.length,
      apiTicketsCount: apiTickets.length,
      optimisticTicketsCount: optimisticCount,
      ticketsUsed: cachedTickets.length > 0 ? 'cached' : 'api',
    });
  }

  // LAZY LOADING: Always fetch ticket details on demand
  // This enables progressive loading - cards first, then details when clicked
  const selectedTicketQuery = useRealtimeTicket(
    tenantId || '',
    selectedTicketId || '',
    !!selectedTicketId && !!tenantId && isSessionActive
  );

  // Messages are now loaded inside TicketDetail component to prevent unnecessary fetches
  // const selectedTicketMessagesQuery = useTicketMessages(
  //   tenantId || '',
  //   selectedTicketId || '',
  //   !!selectedTicketId && !!tenantId && isSessionActive
  // );

  // DEBUG: Log authentication transition errors in development
  if (process.env.NODE_ENV === 'development' && ticketsQuery.isError) {
    console.log(
      '🔐 Authentication transition error (expected during login/logout):',
      {
        error: ticketsQuery.error,
        isLoaded,
        isSignedIn,
        sessionStatus: clerkAuth.sessionId ? 'active' : 'inactive',
        sessionId: clerkAuth.sessionId,
        isSessionActive,
        tenantId,
      }
    );
  }

  // DEBUG: Log selected ticket updates
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && selectedTicketId) {
      const ticket = tickets.find((t: TicketCard) => t.id === selectedTicketId);
      console.log('📋 Selected ticket update:', {
        selectedTicketId,
        ticketStatus: ticket?.status,
        ticketsCount: tickets.length,
        ticketFound: !!ticket,
      });
    }
  }, [selectedTicketId, tickets]);

  // LAZY LOADING: Use full ticket details from query, not from cards list
  // This ensures we have complete ticket data when displaying details
  let selectedTicket = null;

  if (selectedTicketId) {
    // CRITICAL: Only use data from the query, never fall back to card data during loading
    if (selectedTicketQuery.data) {
      selectedTicket = selectedTicketQuery.data;
      console.log('✅ Using cached ticket data for:', selectedTicketId);
    } else if (
      selectedTicketQuery.isLoading ||
      selectedTicketQuery.isFetching
    ) {
      // While loading or fetching, show skeleton by keeping selectedTicket as null
      selectedTicket = null;
      console.log(
        '⏳ Loading ticket, will show skeleton for:',
        selectedTicketId
      );
    } else if (selectedTicketQuery.isError) {
      // On error, we could show an error state or retry
      selectedTicket = null;
      console.log(
        '❌ Error loading ticket:',
        selectedTicketId,
        selectedTicketQuery.error
      );
    } else {
      // No data, not loading, not error - this shouldn't happen
      selectedTicket = null;
      console.log('⚠️ Unexpected state for ticket:', selectedTicketId, {
        hasData: !!selectedTicketQuery.data,
        isLoading: selectedTicketQuery.isLoading,
        isFetching: selectedTicketQuery.isFetching,
        isError: selectedTicketQuery.isError,
      });
    }
  }

  // Loading state is now handled earlier in the component to prevent double loading screens

  // Loading state is now handled earlier in the component to prevent double loading screens

  // Get the correct ticket workflow with proper field transformation
  const {
    handleSubmitTicket,
    expandSection,
    showDraftConfirmation,
    showReplyDraftConfirmation,
    isCreatingTicket: workflowIsCreatingTicket,
    handleTicketSelect: workflowHandleTicketSelect,
    handleCreateTicket: workflowHandleCreateTicket,
    handleCancelCreateTicket,
    handleSaveChanges,
    handleDiscardChanges,
    handleDialogClose,
    handleReplyDraftSave,
    handleReplyDraftDiscard,
    handleReplyDraftCancel,
  } = useTicketWorkflow(
    tickets as unknown as Ticket[], // TicketCard[] is compatible with Ticket[] for workflow purposes
    tenantId,
    true, // isCacheLoaded
    true // hasInitialApiLoad
  );

  // LAZY LOADING: Enhanced skeleton detection for on-demand loading
  const isLoadingSelectedTicket = selectedTicketQuery.isLoading;

  // Check if we have cached full ticket data
  const hasCachedFullTicket =
    selectedTicketQuery.data && !selectedTicketQuery.isLoading;

  // SIMPLIFIED: Show skeleton when we have a selected ticket ID but no ticket data
  // This covers all cases where skeleton should appear
  const shouldShowTicketSkeleton = selectedTicketId && !selectedTicket;

  // Check if we're in a transition state
  const isTransitioning = false; // Simplified - not needed anymore

  // Debug skeleton logic
  useEffect(() => {
    if (selectedTicketId) {
      console.log('🔍 Skeleton Logic Debug:', {
        selectedTicketId,
        isLoadingSelectedTicket,
        hasCachedFullTicket,
        shouldShowTicketSkeleton,
        isTransitioning,
        hasSelectedTicket: !!selectedTicket,
        selectedTicketQueryData: !!selectedTicketQuery.data,
        selectedTicketQueryIsLoading: selectedTicketQuery.isLoading,
        selectedTicketQueryIsFetching: selectedTicketQuery.isFetching,
        selectedTicketQueryIsError: selectedTicketQuery.isError,
        shouldShowSkeleton: !selectedTicket && selectedTicketId,
        ticketDetailWillShowSkeleton: selectedTicketId && !selectedTicket,
      });
    }
  }, [
    selectedTicketId,
    isLoadingSelectedTicket,
    hasCachedFullTicket,
    shouldShowTicketSkeleton,
    isTransitioning,
    selectedTicket,
    selectedTicketQuery.data,
    selectedTicketQuery.isLoading,
    selectedTicketQuery.isFetching,
    selectedTicketQuery.isError,
  ]);

  // Use workflow handlers and state that include draft management
  const handleTicketSelect = useCallback(
    async (ticketId: string | null) => {
      // Continue with normal selection for instant UI response
      if (ticketId !== null) {
        workflowHandleTicketSelect(ticketId);
      } else {
        // Handle null case - deselect ticket
        setSelectedTicketId(null);
      }
    },
    [workflowHandleTicketSelect, setSelectedTicketId]
  );
  const handleCreateTicket = workflowHandleCreateTicket;
  const isCreatingTicket = workflowIsCreatingTicket;

  // CRITICAL FIX: Handle ticket opening to expand "My Open Tickets" section
  const handleTicketOpened = useCallback((ticketId: string) => {
    // For agents, when they open a ticket, it moves to "My Open Tickets"
    // The useTicketWorkflow hook will automatically detect the section change
    // and expand the correct section
    console.log('Ticket opened:', ticketId);
  }, []);

  // Single loading state to prevent double loading screens
  if (!canRender) {
    return null; // Let SessionValidator handle authentication redirects
  }

  // For localhost, show welcome message if not authenticated
  if (domainInfo?.isLocalhost && !user) {
    return (
      <div className='min-h-screen flex items-center justify-center bg-gray-50'>
        <div className='max-w-md w-full space-y-8 p-8 text-center'>
          <h1 className='text-4xl font-bold text-gray-900 mb-4'>
            Welcome to QuantumNest
          </h1>
          <p className='text-lg text-gray-600 mb-8'>
            This is a multi-tenant support ticketing system. To access tickets,
            please visit your organization&apos;s subdomain or sign in.
          </p>
          <Link
            href='/sign-in'
            className='inline-block bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors'
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  // For subdomains, authentication is required (handled by middleware)
  // If we reach here, user is authenticated and ready to show dashboard

  return (
    <AppLayout rightSidebar={<VisitorInformation />}>
      <div className='flex h-full'>
        {/* Recent Tickets Sidebar */}
        <div className='w-96 shrink-0 h-full flex flex-col'>
          <div className='flex-1'>
            <RecentTickets
              selectedTicketId={selectedTicketId}
              onTicketSelect={handleTicketSelect}
              onCreateTicket={handleCreateTicket}
              tickets={tickets}
              isLoading={false} // Loading handled at page level now
              expandSection={expandSection}
              tenantId={tenantId}
            />
          </div>
        </div>
        {/* Main Content - Ticket Detail or Create Form */}
        <div className='flex-1 p-6 h-full overflow-hidden'>
          {/* CRITICAL FIX: Persistent white container to prevent background flashing during transitions */}
          <div className='h-full rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-sm shadow-gray-200/50 dark:shadow-gray-900/50 flex flex-col'>
            {selectedTicketId ? (
              // CRITICAL FIX: Always prioritize showing detail page when we have a selected ticket
              // This handles the optimistic UI case where selectedTicketId is set but isCreatingTicket might still be true
              selectedTicket ? (
                <TicketDetail
                  ticket={selectedTicket}
                  onTicketOpened={handleTicketOpened}
                />
              ) : (
                // Show loading state while optimistic ticket is being resolved
                <TicketDetail
                  ticket={null}
                  onTicketOpened={handleTicketOpened}
                />
              )
            ) : isCreatingTicket ? (
              <CreateTicketForm
                onSubmit={async (data) => {
                  // CRITICAL FIX: Let TanStack Query mutation handle optimistic UI entirely
                  // This prevents duplicate optimistic entries and unnecessary loading states
                  try {
                    await handleSubmitTicket(data);
                    // handleSubmitTicket already handles the optimistic UI and ticket selection
                  } catch (error) {
                    // Error handling is already done in handleSubmitTicket
                    console.error('Failed to create ticket:', error);
                  }
                }}
                onDiscard={() => {
                  // Clear selection when discarding create form
                  setSelectedTicketId(null);
                  handleCancelCreateTicket();
                }}
                isSubmitting={false}
                tenantId={tenantId || undefined}
              />
            ) : (
              // Placeholder - only shown for agents with no tickets
              <div className='flex-1 flex items-center justify-center'>
                <div className='text-center'>
                  <p className='text-gray-500 dark:text-gray-400 mb-2'>
                    {tickets.length === 0
                      ? role === 'agent'
                        ? 'No tickets assigned to you'
                        : 'No tickets available'
                      : role === 'agent'
                        ? 'Select a ticket to view details'
                        : 'Click "Create New Ticket" to get started'}
                  </p>
                  {tickets.length === 0 && !hasPermission('tickets.create') && (
                    <p className='text-sm text-gray-400 dark:text-gray-500'>
                      Contact your administrator to get tickets assigned to you
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Draft Confirmation Dialog for Ticket Creation */}
      <Dialog open={showDraftConfirmation} onOpenChange={handleDialogClose}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Unsaved Draft</DialogTitle>
            <DialogDescription>
              You have unsaved changes in your ticket draft. What would you like
              to do?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className='gap-2'>
            <Button variant='outline' onClick={handleDiscardChanges}>
              Discard Changes
            </Button>
            <Button onClick={handleSaveChanges}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reply Draft Confirmation Dialog */}
      <ReplyDraftConfirmDialog
        open={showReplyDraftConfirmation}
        onOpenChange={handleReplyDraftCancel}
        onDiscard={handleReplyDraftDiscard}
        onSave={handleReplyDraftSave}
      />
    </AppLayout>
  );
}

export default function TicketsPage() {
  return <TicketsPageContent />;
}
