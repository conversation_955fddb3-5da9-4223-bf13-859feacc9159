import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { createServiceSupabaseClient } from '@/lib/supabase-server';
import { z } from 'zod';
import { SupabaseClient } from '@supabase/supabase-js';
import { Json } from '@/types/supabase';

const OpenTicketSchema = z.object({
  tenant_id: z.string().min(1),
});

async function getTenantUuid(
  serviceSupabase: SupabaseClient,
  tenantParam: string
) {
  if (
    !tenantParam.match(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    )
  ) {
    const { data: tenantData, error: tenantError } = await serviceSupabase
      .from('tenants')
      .select('id')
      .eq('subdomain', tenantParam)
      .single();
    if (tenantError || !tenantData) {
      throw new Error(`Tenant '${tenantParam}' not found`);
    }
    return tenantData.id;
  }
  return tenantParam;
}

async function validateUserAccess(
  serviceSupabase: SupabaseClient,
  userId: string,
  tenantUuid: string
) {
  const { data: userData, error: userError } = await serviceSupabase
    .from('users')
    .select('id, tenant_id, role, status')
    .eq('clerk_id', userId)
    .single();
  if (userError || !userData) {
    throw new Error('User not found');
  }
  if (userData.tenant_id !== tenantUuid) {
    throw new Error('Access denied to this tenant');
  }
  return userData;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ ticketId: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { ticketId } = await params;
    const body = await request.json();
    const { tenant_id } = OpenTicketSchema.parse(body);

    // Get user role from Clerk JWT claims
    const clerkRole = (sessionClaims as { o?: { rol?: string } })?.o?.rol;
    const userRole = (() => {
      switch (clerkRole) {
        case 'super_admin':
          return 'super_admin';
        case 'admin':
          return 'admin';
        case 'agent':
          return 'agent';
        case 'member':
          return 'user';
        default:
          return 'user';
      }
    })();

    // Only agents and admins can open tickets
    if (
      userRole !== 'agent' &&
      userRole !== 'admin' &&
      userRole !== 'super_admin'
    ) {
      return NextResponse.json(
        { error: 'Only agents and admins can open tickets' },
        { status: 403 }
      );
    }

    const serviceSupabase = createServiceSupabaseClient();
    const tenantUuid = await getTenantUuid(serviceSupabase, tenant_id);
    const userData = await validateUserAccess(
      serviceSupabase,
      userId,
      tenantUuid
    );

    // Get the current ticket to verify it's assigned to this agent
    const { data: currentTicket, error: ticketError } = await serviceSupabase
      .from('tickets')
      .select('id, assigned_to, status, metadata')
      .eq('id', ticketId)
      .eq('tenant_id', tenantUuid)
      .single();

    if (ticketError || !currentTicket) {
      return NextResponse.json({ error: 'Ticket not found' }, { status: 404 });
    }

    // SIMPLIFIED: For agents, if ticket is unassigned, assign it to them automatically
    // This makes the workflow simpler - one action instead of two
    const needsAssignment = userRole === 'agent' && !currentTicket.assigned_to;

    // For agents with already assigned tickets, verify it's assigned to them
    if (
      userRole === 'agent' &&
      currentTicket.assigned_to &&
      currentTicket.assigned_to !== userData.id
    ) {
      return NextResponse.json(
        { error: 'Ticket is already assigned to another agent' },
        { status: 403 }
      );
    }

    // Verify the ticket is in 'new' status
    if (currentTicket.status !== 'new') {
      return NextResponse.json(
        { error: 'Ticket is not in new status' },
        { status: 400 }
      );
    }

    // Update ticket status to 'open' and add metadata
    const metadata: Json = {
      ...((currentTicket.metadata as Record<string, Json>) || {}),
      opening: {
        opened_by: userData.id,
        opened_at: new Date().toISOString(),
        opened_by_role: userData.role,
      },
    };

    // If agent needs assignment, add assignment metadata too
    if (needsAssignment) {
      metadata.assignment = {
        assigned_by: userData.id,
        assigned_at: new Date().toISOString(),
        assigned_by_role: userData.role,
        self_assigned: true,
      };
    }

    // Build update object
    const updateData: Record<string, unknown> = {
      status: 'open',
      metadata,
      updated_at: new Date().toISOString(),
    };

    // If needs assignment, update assignment fields too
    if (needsAssignment) {
      updateData.assigned_to = userData.id;
      updateData.assigned_to_clerk_id = userId;
      updateData.assigned_by = userData.id;
      updateData.assigned_by_clerk_id = userId;
      updateData.assigned_at = new Date().toISOString();
    }

    const { data: updatedTicket, error: updateError } = await serviceSupabase
      .from('tickets')
      .update(updateData)
      .eq('id', ticketId)
      .eq('tenant_id', tenantUuid)
      .select()
      .single();

    if (updateError) {
      return NextResponse.json(
        { error: 'Failed to open ticket' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: needsAssignment
        ? 'Ticket assigned and opened successfully'
        : 'Ticket opened successfully',
      ticket: {
        id: updatedTicket.id,
        status: updatedTicket.status,
        openedAt: new Date().toISOString(),
        ...(needsAssignment && {
          assignedTo: userData.id,
          assignedAt: new Date().toISOString(),
        }),
      },
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Internal server error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
