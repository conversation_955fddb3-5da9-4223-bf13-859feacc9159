/**
 * Dexie Cache Manager - 2025 Simple Pattern
 *
 * Handles saving/loading React Query cache to/from IndexedDB.
 * Minimal logic, maximum effectiveness.
 */

import { QueryClient } from '@tanstack/react-query';
import {
  cacheDB,
  type CachedTicket,
  type CachedUserProfile,
  type CachedAttachment,
} from './ticket-cache-db';
import { QueryKeys } from '@/lib/query-keys';
import type {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';

export class DexieCacheManager {
  constructor(private queryClient: QueryClient) {}

  /**
   * Save current React Query cache to IndexedDB
   */
  async persistCache(tenantId: string): Promise<void> {
    try {
      // Try to get tickets from various query keys (different components might use different keys)
      let tickets = this.queryClient.getQueryData(
        QueryKeys.TICKETS.list(tenantId)
      ) as Ticket[] | undefined;

      // If not found, try other common query keys
      if (!tickets) {
        tickets = this.queryClient.getQueryData([
          'tickets',
          tenantId,
          'list',
        ]) as Ticket[] | undefined;
      }

      if (!tickets || tickets.length === 0) return;

      // Add cache metadata
      const cachedTickets: CachedTicket[] = tickets.map((ticket) => ({
        ...ticket,
        _cachedAt: Date.now(),
        _syncedAt: Date.now(),
      }));

      // Save to IndexedDB (bulk update)
      await cacheDB.tickets.bulkPut(cachedTickets);

      // Save metadata for sync tracking
      await cacheDB.queryMetadata.put({
        queryKey: `tickets-${tenantId}`,
        lastFetched: Date.now(),
        lastModified: Date.now(),
      });

      console.log(`💾 Persisted ${tickets.length} tickets to IndexedDB`);

      // Also persist user profiles that are in React Query cache
      await this.persistUserProfiles(tenantId);

      // Persist attachments and messages
      await this.persistAttachmentsAndMessages(tenantId);
    } catch (error) {
      // Silent fail - cache is optional enhancement
      console.warn('Cache persist failed:', error);
    }
  }

  /**
   * Load cache from IndexedDB with smart filtering
   */
  async restoreCache(tenantId: string): Promise<void> {
    try {
      // Smart filter: last 48 hours + all non-closed tickets
      const twoDaysAgo = Date.now() - 48 * 60 * 60 * 1000;

      const cachedTickets = await cacheDB.tickets
        .where('tenantId')
        .equals(tenantId)
        .filter(
          (ticket) =>
            ticket.status !== 'closed' ||
            new Date(ticket.updatedAt).getTime() > twoDaysAgo
        )
        .toArray();

      if (cachedTickets.length > 0) {
        // Strip cache metadata and restore to React Query
        const tickets = cachedTickets.map((cachedTicket) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { _cachedAt, _syncedAt, ...ticket } = cachedTicket;
          return ticket;
        });

        this.queryClient.setQueryData(
          QueryKeys.TICKETS.list(tenantId),
          tickets
        );
      }

      // Also restore user profiles
      await this.restoreUserProfiles(tenantId);

      // Restore attachments and messages
      await this.restoreAttachmentsAndMessages(tenantId);
    } catch (error) {
      // Silent fail - will fetch fresh if cache fails
      console.warn('Cache restore failed:', error);
    }
  }

  /**
   * Clean up old closed tickets (run occasionally)
   */
  async pruneCache(tenantId: string): Promise<void> {
    try {
      const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;

      await cacheDB.tickets
        .where('tenantId')
        .equals(tenantId)
        .filter(
          (ticket) =>
            ticket.status === 'closed' &&
            new Date(ticket.updatedAt).getTime() < thirtyDaysAgo
        )
        .delete();
    } catch (error) {
      console.warn('Cache prune failed:', error);
    }
  }

  /**
   * Persist user profiles to IndexedDB
   */
  private async persistUserProfiles(tenantId: string): Promise<void> {
    try {
      // Get all user profiles from React Query cache
      const queryCache = this.queryClient.getQueryCache();
      const userQueries = queryCache.findAll({
        predicate: (query) => {
          const key = query.queryKey;
          return (
            Array.isArray(key) &&
            key[0] === 'users' &&
            key[1] === tenantId &&
            key[2] === 'detail'
          );
        },
      });

      const userProfiles: CachedUserProfile[] = [];
      userQueries.forEach((query) => {
        const data = query.state.data as CachedUserProfile | undefined;
        if (data && typeof data === 'object' && 'id' in data) {
          userProfiles.push({
            ...data,
            tenantId,
            _cachedAt: Date.now(),
          });
        }
      });

      if (userProfiles.length > 0) {
        await cacheDB.userProfiles.bulkPut(userProfiles);
        console.log(
          `💾 Persisted ${userProfiles.length} user profiles to IndexedDB`
        );
      }
    } catch (error) {
      console.warn('User profile persist failed:', error);
    }
  }

  /**
   * Restore user profiles from IndexedDB
   */
  private async restoreUserProfiles(tenantId: string): Promise<void> {
    try {
      const cachedProfiles = await cacheDB.userProfiles
        .where('tenantId')
        .equals(tenantId)
        .toArray();

      if (cachedProfiles.length > 0) {
        // Restore each profile to React Query cache
        cachedProfiles.forEach((profile) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { _cachedAt, ...userProfile } = profile;
          this.queryClient.setQueryData(
            QueryKeys.USERS.detail(tenantId, profile.id),
            userProfile
          );
        });
        console.log(
          `🚀 Restored ${cachedProfiles.length} user profiles from IndexedDB`
        );
      }
    } catch (error) {
      console.warn('User profile restore failed:', error);
    }
  }

  /**
   * Persist attachments and messages to IndexedDB
   */
  private async persistAttachmentsAndMessages(tenantId: string): Promise<void> {
    try {
      // Get all messages from React Query cache for this tenant
      const queryCache = this.queryClient.getQueryCache();
      const messageQueries = queryCache.findAll({
        predicate: (query) => {
          const key = query.queryKey;
          return (
            Array.isArray(key) &&
            key[0] === 'tickets' &&
            key[1] === tenantId &&
            key[3] === 'messages'
          );
        },
      });

      const allAttachments: CachedAttachment[] = [];
      const allMessages: (TicketMessage & {
        ticketId: string;
        _cachedAt: number;
      })[] = [];

      // Extract messages and attachments from query cache
      messageQueries.forEach((query) => {
        const messages = query.state.data as TicketMessage[] | undefined;
        if (messages && Array.isArray(messages)) {
          const ticketId = (
            query.queryKey as [string, string, string, string]
          )[2];

          messages.forEach((message) => {
            allMessages.push({
              ...message,
              ticketId,
              _cachedAt: Date.now(),
            });

            // Extract attachments from messages
            if (message.attachments && message.attachments.length > 0) {
              message.attachments.forEach((attachment) => {
                allAttachments.push({
                  ...attachment,
                  messageId: message.id,
                  ticketId,
                  tenantId,
                  _cachedAt: Date.now(),
                });
              });
            }
          });
        }
      });

      // Also get attachments directly from tickets
      const tickets = this.queryClient.getQueryData(
        QueryKeys.TICKETS.list(tenantId)
      ) as Ticket[] | undefined;

      if (tickets) {
        tickets.forEach((ticket) => {
          if (ticket.attachments && ticket.attachments.length > 0) {
            ticket.attachments.forEach((attachment) => {
              // Only add if not already added from messages
              if (!allAttachments.find((a) => a.id === attachment.id)) {
                allAttachments.push({
                  ...attachment,
                  ticketId: ticket.id,
                  tenantId,
                  _cachedAt: Date.now(),
                });
              }
            });
          }
        });
      }

      // Persist to IndexedDB
      if (allMessages.length > 0) {
        await cacheDB.messages.bulkPut(allMessages);
        console.log(`💾 Persisted ${allMessages.length} messages to IndexedDB`);
      }

      if (allAttachments.length > 0) {
        await cacheDB.attachments.bulkPut(allAttachments);
        console.log(
          `💾 Persisted ${allAttachments.length} attachments to IndexedDB`
        );
      }
    } catch (error) {
      console.warn('Attachment/message persist failed:', error);
    }
  }

  /**
   * Restore attachments and messages from IndexedDB
   */
  private async restoreAttachmentsAndMessages(tenantId: string): Promise<void> {
    try {
      // Restore messages by ticket
      const cachedMessages = await cacheDB.messages
        .where('ticketId')
        .notEqual('')
        .toArray();

      // Group messages by ticket
      const messagesByTicket = new Map<string, TicketMessage[]>();
      cachedMessages.forEach((message) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { _cachedAt, ...ticketMessage } = message;
        const ticketId = message.ticketId;

        if (!messagesByTicket.has(ticketId)) {
          messagesByTicket.set(ticketId, []);
        }
        messagesByTicket.get(ticketId)!.push(ticketMessage as TicketMessage);
      });

      // Restore to React Query cache
      messagesByTicket.forEach((messages, ticketId) => {
        this.queryClient.setQueryData(
          QueryKeys.TICKETS.messages(tenantId, ticketId),
          messages
        );
      });

      if (messagesByTicket.size > 0) {
        console.log(
          `🚀 Restored messages for ${messagesByTicket.size} tickets from IndexedDB`
        );
      }

      // Restore attachments
      const cachedAttachments = await cacheDB.attachments
        .where('tenantId')
        .equals(tenantId)
        .toArray();

      if (cachedAttachments.length > 0) {
        // Group attachments by ticket and message
        const attachmentsByTicket = new Map<
          string,
          Omit<CachedAttachment, '_cachedAt'>[]
        >();
        cachedAttachments.forEach((attachment) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { _cachedAt, ...cleanAttachment } = attachment;

          if (attachment.ticketId) {
            if (!attachmentsByTicket.has(attachment.ticketId)) {
              attachmentsByTicket.set(attachment.ticketId, []);
            }
            attachmentsByTicket.get(attachment.ticketId)!.push(cleanAttachment);
          }
        });

        console.log(
          `🚀 Restored ${cachedAttachments.length} attachments from IndexedDB`
        );
      }
    } catch (error) {
      console.warn('Attachment/message restore failed:', error);
    }
  }
}
