/**
 * Unified Real-time Subscription Hook - 2025 Clean Architecture
 *
 * Main hook that combines all real-time modules.
 * Simple, clean API for components.
 */

import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useSupabaseClient } from '@/lib/supabase-clerk';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { RealtimeConnectionManager } from './connection-manager';
import { RealtimeEventHandlers } from './event-handlers';
import { FallbackManager } from './fallback-manager';

export function useRealtimeSubscription(tenantId: string, enabled = true) {
  const { supabase } = useSupabaseClient();
  const queryClient = useQueryClient();
  const { isLoggingOut } = useAuth();
  const subscriberIdRef = useRef(`sub-${Date.now()}-${Math.random()}`);
  const fallbackManagerRef = useRef<FallbackManager | null>(null);

  useEffect(() => {
    if (!enabled || !tenantId || !supabase || isLoggingOut) return;

    const subscriberId = subscriberIdRef.current;

    // Get singleton connection manager
    const connectionManager = RealtimeConnectionManager.getInstance(
      tenantId,
      supabase
    );

    // Create event handlers
    const eventHandlers = new RealtimeEventHandlers(queryClient, tenantId);

    // Create fallback manager
    const fallbackManager = new FallbackManager(queryClient, tenantId);
    fallbackManagerRef.current = fallbackManager;

    // Add subscriber and get channel
    const channel = connectionManager.addSubscriber(subscriberId);

    if (channel) {
      // Setup event listeners
      eventHandlers.setupEventListeners(channel);

      // Subscribe to channel
      channel.subscribe((status) => {
        console.log(`📡 Real-time subscription status: ${status}`);
      });
    }

    // Monitor connection status
    const unsubscribe = connectionManager.onConnectionChange((connected) => {
      if (connected) {
        fallbackManager.stopFallback();
      } else {
        fallbackManager.startFallback();
      }
    });

    return () => {
      connectionManager.removeSubscriber(subscriberId);
      RealtimeConnectionManager.cleanup(tenantId);
      fallbackManager.stopFallback();
      unsubscribe();
    };
  }, [enabled, tenantId, supabase, queryClient, isLoggingOut]);

  // Return fallback manager for manual validation
  return {
    validateTicket: (ticketId: string) => {
      fallbackManagerRef.current?.validateSingleTicket(ticketId);
    },
    forceValidation: () => {
      fallbackManagerRef.current?.forceValidation();
    },
  };
}

// Export for backward compatibility
export { useRealtimeSubscription as useUnifiedRealtimeSubscription };
