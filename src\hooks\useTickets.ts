/**
 * Custom React Query Hooks for Tickets - 2025 Optimized
 *
 * These hooks abstract all data-fetching logic and reduce component code by 90%
 * Following TkDodo's best practices for React Query v5
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ticketQueryOptions } from '@/lib/query-options';
import { QueryKeys, CACHE_CONFIG } from '@/lib/query-keys';
import type {
  Ticket,
  TicketMessage,
} from '@/features/ticketing/models/ticket.schema';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useUserDatabaseId } from '@/features/shared/hooks/useUserDatabaseId';
import { useTenantUuid } from '@/hooks/useTenantUuid';
import { useUnifiedRealtimeSubscription } from './realtime/useRealtimeSubscription';
import { toast } from '@/features/shared/components/toast';
import {
  usePopulateUserCacheFromTickets,
  usePopulateUserCacheFromMessages,
} from './useUserCache';

interface RoleBasedFilterContext {
  tenantId: string;
  role: string;
  userId?: string;
}

interface TicketFilterOptions {
  status?: string[];
  priority?: string[];
  roleFilter?: 'new' | 'assigned' | 'all';
}

interface CreateTicketData {
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  department: string;
  assigned_to?: string;
  assignedTo?: string; // Support both camelCase and snake_case
  assigned_to_clerk_id?: string;
  assignedToClerkId?: string; // Support both camelCase and snake_case
  cc?: string[];
  attachment_ids?: string[];
}

interface UpdateTicketData {
  title?: string;
  description?: string;
  status?: string;
  priority?: string;
  assigned_to?: string;
}

// Enhanced hook for ticket list with 2025 cache-first strategy
export const useTickets = (
  context: RoleBasedFilterContext,
  options?: TicketFilterOptions & { enabled?: boolean }
) => {
  const { enabled = true, ...filterOptions } = options || {};

  const query = useQuery({
    ...ticketQueryOptions.list(context, filterOptions),
    // Apply cache-first configuration for instant loading
    ...CACHE_CONFIG.tickets,
    enabled,
  });

  // Populate user cache from ticket data
  usePopulateUserCacheFromTickets(
    query.data as Ticket[] | undefined,
    context.tenantId
  );

  return query;
};

// Custom hook for ticket detail with select optimization
export const useTicket = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  return useQuery({
    ...ticketQueryOptions.detail(tenantId, ticketId),
    enabled: enabled && !!tenantId && !!ticketId && !isOptimisticTicket,
  });
};

// Real-time version of useTicket for ticket detail page with unified subscription
export const useRealtimeTicket = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  const { isLoggingOut } = useAuth();

  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  // Resolve tenant UUID first
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Use standard React Query without real-time subscription (handled by unified manager)
  const query = useQuery({
    queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
    queryFn: async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      console.log(
        '🔍 Fetching ticket detail for ticket:',
        ticketId,
        'tenant UUID:',
        tenantUuid
      );

      const response = await fetch(
        `/api/tickets/${ticketId}?tenant_id=${tenantUuid}`
      );

      if (!response.ok) {
        if (response.status === 404) {
          console.warn('⚠️ Ticket not found, removing from cache:', ticketId);
          // Don't throw for 404s - just return null to indicate ticket doesn't exist
          return null;
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch ticket');
      }

      const data = await response.json();
      return data.data;
    },
    enabled:
      enabled &&
      !!tenantId &&
      !!ticketId &&
      !isOptimisticTicket &&
      !!tenantUuid &&
      !isLoggingOut,
    // CRITICAL FIX: Configure retry behavior to avoid hammering non-existent tickets
    retry: (failureCount, error) => {
      // Don't retry 404 errors - ticket doesn't exist
      if (error.message.includes('404')) {
        console.warn('🚫 Not retrying 404 for ticket:', ticketId);
        return false;
      }
      // Retry other errors up to 2 times
      return failureCount < 2;
    },
    ...CACHE_CONFIG.tickets,
    // Override refetchOnMount for ticket details to ensure we fetch if no cache exists
    refetchOnMount: (query) => {
      // If we have no data, always fetch
      if (!query.state.data) return true;
      // If we have data, use the default behavior (check if stale)
      return query.state.dataUpdateCount === 0;
    },
    // IMPORTANT: Don't use placeholderData for ticket details
    // We want to show skeleton when loading uncached tickets
    // Only use cached data when it's actually in the cache, not as placeholder
  });

  // Use unified real-time subscription for automatic cache updates
  useUnifiedRealtimeSubscription(tenantId);

  return query;
};

// REMOVED: useGlobalMessageRealtime - replaced by unified subscription manager

// Custom hook for ticket messages with real-time updates
export const useTicketMessages = (
  tenantId: string,
  ticketId: string,
  enabled = true
) => {
  const { isLoggingOut } = useAuth();

  // CRITICAL FIX: Prevent API calls for optimistic tickets
  const isOptimisticTicket =
    ticketId?.startsWith('optimistic-') || ticketId?.startsWith('temp-');

  // CRITICAL FIX: Resolve tenant UUID for proper real-time subscription
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  // Use unified real-time subscription for automatic cache updates
  useUnifiedRealtimeSubscription(tenantId);

  // Use regular query (not real-time) since unified subscription handles real-time updates
  const query = useQuery({
    queryKey: QueryKeys.TICKETS.messages(tenantUuid || tenantId, ticketId),
    queryFn: async () => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      console.log(
        '🔍 Fetching messages for ticket:',
        ticketId,
        'tenant UUID:',
        tenantUuid
      );

      const response = await fetch(
        `/api/tickets/${ticketId}/messages?tenant_id=${tenantUuid}`
      );

      if (!response.ok) {
        if (response.status === 404) {
          console.warn(
            '⚠️ Ticket messages not found, ticket may not exist:',
            ticketId
          );
          // Return empty array for non-existent tickets
          return [];
        }
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to fetch messages');
      }

      const data = await response.json();
      return data.messages || [];
    },
    enabled:
      enabled &&
      !!tenantId &&
      !!ticketId &&
      !isOptimisticTicket &&
      !!tenantUuid &&
      !isLoggingOut,
    ...CACHE_CONFIG.messages,
    // CRITICAL: Use previous data while fetching to show cached data instantly
    placeholderData: (previousData) => previousData,
  });

  // Populate user cache from message data
  usePopulateUserCacheFromMessages(query.data, tenantId);

  return query;
};

// Custom mutation hook for opening tickets with optimistic updates
export const useOpenTicket = (
  tenantId: string,
  onTicketOpened?: (ticketId: string) => void
) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async (ticketId: string) => {
      // Get tenant ID from the current subdomain
      const hostname = window.location.hostname;
      const parts = hostname.split('.');
      const currentTenantId = parts.length > 1 ? parts[0] : null;

      if (
        !currentTenantId ||
        currentTenantId === 'localhost' ||
        currentTenantId === 'www'
      ) {
        throw new Error('Tenant ID not available');
      }

      const response = await fetch(`/api/tickets/${ticketId}/open`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tenant_id: currentTenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `HTTP ${response.status}: ${response.statusText}`
        );
      }

      return response.json();
    },
    onMutate: async (ticketId: string) => {
      console.log(
        '🚀 Starting optimistic update for opening ticket:',
        ticketId
      );

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
      });

      // CRITICAL FIX: Snapshot previous value (could be single ticket or array)
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId)
      );

      // CRITICAL FIX: Optimistically update the ticket status to 'open' as array to match useRealtimeQuery format
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId),
        (old: Ticket | Ticket[] | undefined) => {
          // Handle both single ticket and array formats for compatibility
          const currentTicket = Array.isArray(old) ? old[0] : old;
          if (!currentTicket) return old;

          // SIMPLIFIED: Check if ticket needs assignment (unassigned and agent role)
          const userRole = user?.publicMetadata?.role || 'user';
          const needsAssignment =
            userRole === 'agent' && !currentTicket.assignedTo;

          // CRITICAL FIX: Preserve all existing metadata
          const existingMetadata =
            (currentTicket.metadata as Record<string, unknown>) || {};

          const updatedTicket = {
            ...currentTicket,
            status: 'open' as const,
            updatedAt: new Date(),
            // If agent opening unassigned ticket, assign it to them
            ...(needsAssignment && {
              assignedTo: userDatabaseId || user?.id || '',
              assignedToClerkId: user?.id || '',
              assignedBy: userDatabaseId || user?.id || '',
              assignedByClerkId: user?.id || '',
              assignedAt: new Date(),
            }),
            metadata: {
              ...existingMetadata,
              // Add opening metadata
              opening: {
                opened_by: user?.id,
                opened_at: new Date().toISOString(),
                opened_by_role: userRole,
              },
              // Add assignment metadata if needed
              ...(needsAssignment && {
                assignment: {
                  assigned_by: user?.id,
                  assigned_at: new Date().toISOString(),
                  assigned_by_role: userRole,
                  self_assigned: true,
                },
              }),
            },
          };

          console.log('📝 Updated ticket detail:', {
            oldStatus: currentTicket.status,
            newStatus: updatedTicket.status,
          });

          // Return in the same format as received
          return Array.isArray(old) ? [updatedTicket] : updatedTicket;
        }
      );

      // CRITICAL FIX: Update all possible ticket list query keys to ensure immediate UI updates
      // This covers all the different query key patterns used across the app
      const queryKeysToUpdate = [
        ['tickets', tenantId, 'list'],
        ['tickets', tenantUuid, 'list'],
        ['realtime-tickets', tenantId],
        ['realtime-tickets', tenantUuid],
        // CRITICAL FIX: Also update role-specific query keys
        QueryKeys.TICKETS.list(tenantId, { roleFilter: 'assigned' }),
        QueryKeys.TICKETS.list(tenantId, { roleFilter: 'all' }),
        QueryKeys.TICKETS.list(tenantId, { roleFilter: 'new' }),
        QueryKeys.TICKETS.list(tenantUuid || '', { roleFilter: 'assigned' }),
        QueryKeys.TICKETS.list(tenantUuid || '', { roleFilter: 'all' }),
        QueryKeys.TICKETS.list(tenantUuid || '', { roleFilter: 'new' }),
      ];

      queryKeysToUpdate.forEach((queryKeyPattern) => {
        queryClient.setQueriesData(
          {
            queryKey: Array.isArray(queryKeyPattern)
              ? queryKeyPattern
              : [queryKeyPattern],
            exact: false,
          },
          (old: Ticket[] | undefined) => {
            if (!old) return old;

            console.log('📋 Updating ticket list:', {
              queryKey: queryKeyPattern,
              ticketsCount: old.length,
              targetTicketId: ticketId,
            });

            return old.map((ticket) => {
              if (ticket.id !== ticketId) return ticket;

              // SIMPLIFIED: Check if ticket needs assignment
              const userRole = user?.publicMetadata?.role || 'user';
              const needsAssignment =
                userRole === 'agent' && !ticket.assignedTo;

              return {
                ...ticket,
                status: 'open' as const,
                updatedAt: new Date(),
                // If agent opening unassigned ticket, assign it to them
                ...(needsAssignment && {
                  assignedTo: userDatabaseId || user?.id || '',
                  assignedToClerkId: user?.id || '',
                  assignedBy: userDatabaseId || user?.id || '',
                  assignedByClerkId: user?.id || '',
                  assignedAt: new Date(),
                }),
                metadata: {
                  ...((ticket.metadata as Record<string, unknown>) || {}),
                  opening: {
                    opened_by: user?.id,
                    opened_at: new Date().toISOString(),
                    opened_by_role: userRole,
                  },
                  ...(needsAssignment && {
                    assignment: {
                      assigned_by: user?.id,
                      assigned_at: new Date().toISOString(),
                      assigned_by_role: userRole,
                      self_assigned: true,
                    },
                  }),
                },
              };
            });
          }
        );
      });

      return { previousTicket };
    },
    onSuccess: (_data, ticketId) => {
      toast.success('Ticket Opened', {
        description: 'You can now reply to this ticket.',
        duration: 3000,
      });

      // CRITICAL FIX: Force immediate re-render of components that depend on ticket lists
      // This ensures UI updates immediately without waiting for polling
      queryClient.invalidateQueries({
        queryKey: ['tickets'],
        exact: false,
        refetchType: 'active', // Force active queries to refetch immediately
      });

      // Trigger callback to handle UI updates like section expansion
      if (onTicketOpened) {
        onTicketOpened(ticketId);

        // CRITICAL FIX: For agents, also trigger callback after refetch completes
        // This ensures the ticket is in the list before section expansion logic runs
        if (user?.publicMetadata?.role === 'agent') {
          // Wait for the refetch triggered in onSettled to complete
          setTimeout(() => {
            console.log('⏰ Triggering delayed section expansion check');
            onTicketOpened(ticketId);
          }, 300);
        }
      }
    },
    onError: (_err, ticketId, context) => {
      // CRITICAL FIX: Rollback on error maintaining original format
      if (context?.previousTicket) {
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(tenantId, ticketId),
          context.previousTicket
        );
      }

      toast.error('Failed to Open Ticket', {
        description: 'An unexpected error occurred. Please try again.',
        duration: 5000,
      });
    },
    onSettled: (data, error, ticketId) => {
      const currentTenantUuid = tenantUuid || tenantId;

      if (error) {
        // Invalidate on error
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });

        // Also invalidate list queries on error to ensure consistency
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
        });
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      } else if (data && user?.publicMetadata?.role === 'agent') {
        // For agents, immediately refetch the list to include newly assigned ticket
        // This ensures the ticket appears in their "My Open Tickets" section right away
        console.log('🔄 Agent opened ticket, refetching assigned tickets list');

        // Invalidate AND refetch to ensure immediate update
        const assignedQueryKey = QueryKeys.TICKETS.list(tenantId, {
          roleFilter: 'assigned',
        });

        queryClient.invalidateQueries({
          queryKey: assignedQueryKey,
        });

        // Force immediate refetch instead of waiting for next render
        queryClient.refetchQueries({
          queryKey: assignedQueryKey,
        });

        // Also do the same for UUID-based query key
        if (currentTenantUuid && currentTenantUuid !== tenantId) {
          const uuidQueryKey = QueryKeys.TICKETS.list(currentTenantUuid, {
            roleFilter: 'assigned',
          });
          queryClient.invalidateQueries({
            queryKey: uuidQueryKey,
          });
          queryClient.refetchQueries({
            queryKey: uuidQueryKey,
          });
        }
      }
    },
  });
};

// Custom mutation hook with optimistic updates
export const useCreateTicket = (
  tenantId: string,
  onOptimisticTicketCreated?: (ticketId: string) => void,
  onRealTicketCreated?: (
    realTicketId: string,
    optimisticTicketId: string
  ) => void
) => {
  const queryClient = useQueryClient();
  const { user, role } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  return useMutation({
    mutationFn: async (ticketData: CreateTicketData) => {
      const response = await fetch('/api/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...ticketData,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create ticket');
      }

      return response.json();
    },
    onMutate: async (newTicket) => {
      // CRITICAL FIX: Use tenantId (subdomain) for query key matching
      // This ensures optimistic updates match the query keys used by useRealtimeTickets
      const queryKeyTenantId = tenantId;

      // Determine which query key to update based on user role
      const queryKeyToUpdate =
        role === 'agent'
          ? QueryKeys.TICKETS.list(queryKeyTenantId, { roleFilter: 'assigned' })
          : QueryKeys.TICKETS.list(queryKeyTenantId, { roleFilter: 'all' });

      // Cancel outgoing refetches for the specific query
      await queryClient.cancelQueries({
        queryKey: queryKeyToUpdate,
      });

      // Snapshot previous value for rollback
      const previousData = queryClient.getQueryData(queryKeyToUpdate);
      const previousTicketsData = new Map();
      if (previousData) {
        previousTicketsData.set(queryKeyToUpdate, previousData);
      }

      // Create optimistic ticket with unique ID and proper assignment data
      // Use crypto.randomUUID for stable client-side ID generation
      const optimisticId =
        typeof window !== 'undefined' && window.crypto?.randomUUID
          ? `optimistic-${window.crypto.randomUUID()}`
          : `optimistic-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      const optimisticTicket: Ticket = {
        id: optimisticId,
        title: newTicket.title,
        tenantId: queryKeyTenantId,
        createdAt: new Date(),
        updatedAt: new Date(),
        // CRITICAL FIX: Use database UUID for userId to match filtering logic
        userId: userDatabaseId || user?.id || 'temp-user',
        // CRITICAL FIX: Add creatorClerkId for fallback matching in role-based filtering
        creatorClerkId: user?.id || '',
        userName: user
          ? `${user.firstName || ''} ${user.lastName || ''}`.trim() ||
            'Unknown User'
          : 'Creating...',
        userEmail: user?.primaryEmailAddress?.emailAddress || '',
        userAvatar: user?.imageUrl || undefined,
        // CRITICAL FIX: Include the form description as the ticket content for instant display
        description: newTicket.description || newTicket.title || '',
        priority: newTicket.priority,
        department: newTicket.department as 'sales' | 'support' | 'technical',
        messages: [],
        attachments: [], // Will be populated when attachments are uploaded
        status: 'new' as const,
        tags: [],
        metadata: {
          // Store form data in metadata for instant display
          formData: {
            title: newTicket.title,
            description: newTicket.description,
            priority: newTicket.priority,
            department: newTicket.department,
          },
          createdByUser: {
            role: role || user?.publicMetadata?.role || 'user',
            name: user
              ? `${user.firstName || ''} ${user.lastName || ''}`.trim() ||
                'Unknown User'
              : 'Creating...',
            email: user?.primaryEmailAddress?.emailAddress || '',
            avatar: user?.imageUrl,
          },
        },
        // CRITICAL FIX: Include assignment data in optimistic update to prevent timing issues
        // Handle both camelCase (frontend) and snake_case (API) field names
        assignedTo: newTicket.assignedTo || newTicket.assigned_to || undefined,
        assignedToClerkId:
          newTicket.assignedToClerkId ||
          newTicket.assigned_to_clerk_id ||
          undefined,
        // Additional assignment tracking fields for consistency
        assignedBy: undefined,
        assignedByClerkId: undefined,
        assignedAt:
          newTicket.assignedTo || newTicket.assigned_to
            ? new Date()
            : undefined,
      } as Ticket;

      // CRITICAL FIX: Update the ticket list cache
      // Update the appropriate query
      queryClient.setQueryData(
        queryKeyToUpdate,
        (old: Ticket[] | undefined) => {
          console.log('🎯 Updating ticket list cache with optimistic ticket:', {
            tenantId: queryKeyTenantId,
            optimisticId: optimisticTicket.id,
            oldLength: old?.length || 0,
            queryKey: queryKeyToUpdate,
            role,
          });

          if (!old) return [optimisticTicket];

          // CRITICAL FIX: Check if optimistic ticket already exists to prevent duplicates
          const existingIndex = old.findIndex(
            (t) => t.id === optimisticTicket.id
          );
          if (existingIndex !== -1) {
            console.warn(
              '⚠️ Optimistic ticket already exists, skipping duplicate'
            );
            return old;
          }

          return [optimisticTicket, ...old];
        }
      );

      // CRITICAL FIX: Immediately cache the optimistic ticket in detail cache to prevent skeleton loading
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(queryKeyTenantId, optimisticTicket.id),
        optimisticTicket
      );

      // CRITICAL FIX: Initialize empty messages cache for optimistic ticket to prevent loading states
      queryClient.setQueryData(
        QueryKeys.TICKETS.messages(queryKeyTenantId, optimisticTicket.id),
        []
      );

      // CRITICAL FIX: Immediately select the optimistic ticket for instant UI feedback
      if (onOptimisticTicketCreated) {
        onOptimisticTicketCreated(optimisticTicket.id);
      }

      return { previousTicketsData, optimisticTicket };
    },
    onSuccess: (result, _variables, context) => {
      // CRITICAL FIX: Replace optimistic ticket with real ticket data in cache
      // This maintains the ticket position in the list while updating with real data
      const realTicket = result.ticket as Ticket;
      const optimisticTicket = context?.optimisticTicket;

      if (optimisticTicket && realTicket) {
        // CRITICAL FIX: Update only the specific query key that was updated in onMutate
        const queryKeyToUpdate =
          role === 'agent'
            ? QueryKeys.TICKETS.list(tenantId, { roleFilter: 'assigned' })
            : QueryKeys.TICKETS.list(tenantId, { roleFilter: 'all' });

        queryClient.setQueryData(
          queryKeyToUpdate,
          (old: Ticket[] | undefined) => {
            if (!old) return old;

            console.log('🔄 Replacing optimistic ticket with real ticket:', {
              optimisticId: optimisticTicket.id,
              realId: realTicket.id,
              oldLength: old.length,
            });

            // Replace optimistic ticket with real ticket, maintaining position
            return old.map((ticket) =>
              ticket.id === optimisticTicket.id ? realTicket : ticket
            );
          }
        );

        // CRITICAL FIX: Set the real ticket in the detail cache
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(tenantId, realTicket.id),
          realTicket
        );

        // CRITICAL FIX: Initialize empty messages cache for the real ticket
        // This prevents unnecessary loading states when transitioning from optimistic ticket
        queryClient.setQueryData(
          QueryKeys.TICKETS.messages(tenantId, realTicket.id),
          []
        );

        // CRITICAL FIX: Also pre-populate with all possible tenant ID formats
        // to ensure the messages query doesn't show skeleton during transition
        const tenantVariants = new Set([tenantId]);

        // Add the real ticket's tenantId if different
        if (realTicket.tenantId && realTicket.tenantId !== tenantId) {
          tenantVariants.add(realTicket.tenantId);
        }

        // Add the tenant_id from the response if available (API may return tenant_id field)
        const ticketWithTenantId = realTicket as Ticket & {
          tenant_id?: string;
        };
        if (
          ticketWithTenantId.tenant_id &&
          ticketWithTenantId.tenant_id !== tenantId
        ) {
          tenantVariants.add(ticketWithTenantId.tenant_id);
        }

        // Pre-populate messages cache for all tenant ID variants
        tenantVariants.forEach((tid) => {
          if (tid !== tenantId) {
            queryClient.setQueryData(
              QueryKeys.TICKETS.messages(tid, realTicket.id),
              []
            );
          }
        });

        // CRITICAL FIX: Notify about the transition from optimistic to real ticket
        if (onRealTicketCreated) {
          onRealTicketCreated(realTicket.id, optimisticTicket.id);
        }
      }
    },
    onError: (_err, _newTicket, context) => {
      // Rollback on error - restore all previous query data
      if (context?.previousTicketsData) {
        context.previousTicketsData.forEach((data, queryKey) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    onSettled: (_data, error) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic ticket creation should remain stable without refetching
      if (error) {
        queryClient.invalidateQueries({
          queryKey: ['tickets', tenantId, 'list'],
          exact: false,
        });
      }
      // On success, the optimistic ticket is already in place and should remain
    },
  });
};

// Custom mutation hook for updating tickets
export const useUpdateTicket = (tenantId: string) => {
  const queryClient = useQueryClient();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async ({
      ticketId,
      updates,
    }: {
      ticketId: string;
      updates: UpdateTicketData;
    }) => {
      const response = await fetch(`/api/tickets/${ticketId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...updates,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update ticket');
      }

      return response.json();
    },
    onMutate: async ({ ticketId, updates }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.detail(tenantId, ticketId),
      });

      // Snapshot previous value
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId)
      );

      // Optimistically update cache
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(tenantId, ticketId),
        (old: Ticket) =>
          old ? { ...old, ...updates, updatedAt: new Date() } : old
      );

      return { previousTicket };
    },
    onError: (_err, { ticketId }, context) => {
      // Rollback on error
      if (context?.previousTicket) {
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(tenantId, ticketId),
          context.previousTicket
        );
      }
    },
    onSettled: (_data, error, { ticketId }) => {
      // CRITICAL FIX: Only invalidate on error to prevent unnecessary skeleton loading
      // Successful optimistic updates should remain stable without refetching
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
      // On success, the optimistic update is already in place and should remain
    },
  });
};

// Custom hook for adding messages to tickets with optimistic updates
export const useAddTicketMessage = (tenantId: string, ticketId: string) => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { userDatabaseId } = useUserDatabaseId();

  // CRITICAL FIX: Resolve tenant UUID for proper query key matching
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async (messageData: {
      content: string;
      attachment_ids?: string[];
      attachments?: Array<{
        id: string;
        name: string;
        type: string;
        size: number;
      }>;
      is_resolve_action?: boolean;
      new_status?: 'pending' | 'resolved' | 'open';
    }) => {
      if (!tenantUuid) {
        throw new Error('Tenant UUID not resolved');
      }

      const response = await fetch(`/api/tickets/${ticketId}/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...messageData,
          tenant_id: tenantUuid,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to add message');
      }

      return response.json();
    },
    onMutate: async (messageData) => {
      // CRITICAL FIX: Use tenant UUID for proper query key matching
      const currentTenantUuid = tenantUuid || tenantId;

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
      });

      // Snapshot previous values for rollback
      const previousMessages = queryClient.getQueryData(
        QueryKeys.TICKETS.messages(currentTenantUuid, ticketId)
      );
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(currentTenantUuid, ticketId)
      );

      // Create optimistic message with attachments
      const optimisticAttachments = (messageData.attachments || []).map(
        (att) => ({
          id: att.id,
          name: att.name,
          type: att.type,
          size: att.size,
          url: `/api/attachments/${att.id}`,
          uploadedAt: new Date(),
        })
      );

      // Create optimistic message that matches the expected format
      const userName =
        `${user?.firstName || ''} ${user?.lastName || ''}`.trim() ||
        user?.username ||
        'You';

      const optimisticMessage = {
        id: `optimistic-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        content: messageData.content,
        author_id: userDatabaseId || user?.id || '',
        // Include both formats for compatibility
        authorId: userDatabaseId || user?.id || '',
        authorName: userName,
        authorEmail: user?.primaryEmailAddress?.emailAddress || '',
        authorAvatar: user?.imageUrl || undefined,
        // Also include author object format for compatibility
        author: {
          id: userDatabaseId || user?.id || '',
          first_name: user?.firstName || '',
          last_name: user?.lastName || '',
          email: user?.primaryEmailAddress?.emailAddress || '',
          avatar_url: user?.imageUrl || undefined,
          role: user?.publicMetadata?.role || 'user',
        },
        created_at: new Date().toISOString(),
        createdAt: new Date(),
        attachments: optimisticAttachments,
        ticketId: ticketId,
        ticket_id: ticketId,
        message_type: 'message',
        is_internal: false,
      };

      // Optimistically update messages cache
      queryClient.setQueryData(
        QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
        (old: TicketMessage[] | undefined) => {
          if (!old) return [optimisticMessage];
          return [...old, optimisticMessage];
        }
      );

      // Get current ticket data for status determination
      const currentTicket = Array.isArray(previousTicket)
        ? previousTicket[0]
        : previousTicket;

      // Smart status update based on reply action and current user role
      let newStatus: Ticket['status'] | null = null;

      if (messageData.is_resolve_action) {
        // Explicit resolve action - always set to resolved
        newStatus = 'resolved';
      } else if (messageData.new_status) {
        // Explicit status change
        newStatus = messageData.new_status;
      } else if (currentTicket && !messageData.is_resolve_action) {
        // Auto status change based on user role and current status
        const userRole = user?.publicMetadata?.role || 'user';
        const isAgentOrAdmin =
          userRole === 'agent' ||
          userRole === 'admin' ||
          userRole === 'super_admin';

        if (
          isAgentOrAdmin &&
          currentTicket.status !== 'resolved' &&
          currentTicket.status !== 'closed'
        ) {
          // Agent/Admin reply: Change to pending (awaiting user response)
          newStatus = 'pending';
        } else if (!isAgentOrAdmin && currentTicket.status === 'pending') {
          // User reply to pending ticket: Change to open
          newStatus = 'open';
        }
        // For other cases, status remains unchanged
      }

      // Apply optimistic status update if needed
      if (newStatus) {
        // Update ticket detail cache with new status
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
          (old: Ticket | Ticket[] | undefined) => {
            const currentTicket = Array.isArray(old) ? old[0] : old;
            if (!currentTicket) return old;

            const updatedTicket = {
              ...currentTicket,
              status: newStatus as Ticket['status'],
              updatedAt: new Date(),
            };

            return Array.isArray(old) ? [updatedTicket] : updatedTicket;
          }
        );

        // Update all ticket list caches for instant UI feedback
        const queryKeysToUpdate = [
          ['tickets', currentTenantUuid, 'list'],
          ['tickets', tenantId, 'list'],
          ['realtime-tickets', currentTenantUuid],
          ['realtime-tickets', tenantId],
        ];

        queryKeysToUpdate.forEach((queryKeyPattern) => {
          queryClient.setQueriesData(
            {
              queryKey: queryKeyPattern,
              exact: false,
            },
            (old: Ticket[] | undefined) => {
              if (!Array.isArray(old)) return old;
              return old.map((ticket) =>
                ticket.id === ticketId
                  ? {
                      ...ticket,
                      status: newStatus as Ticket['status'],
                      updatedAt: new Date(),
                    }
                  : ticket
              );
            }
          );
        });
      }

      return {
        previousMessages,
        previousTicket,
        expectedStatus: newStatus,
      };
    },
    onSuccess: (_data, variables) => {
      // Don't manually update cache - let real-time subscription handle it
      // This prevents race conditions and flickering

      // Show success toast
      const actionText = variables.is_resolve_action ? 'Resolved' : 'Sent';
      toast.success(`Reply ${actionText}`, {
        description: variables.is_resolve_action
          ? 'Ticket has been resolved successfully.'
          : 'Your reply has been sent.',
        duration: 3000,
      });
    },
    onError: (error, _messageData, context) => {
      const currentTenantUuid = tenantUuid || tenantId;

      // Rollback messages on error
      if (context?.previousMessages) {
        queryClient.setQueryData(
          QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
          context.previousMessages
        );
      }

      // Rollback ticket status on error
      if (context?.previousTicket && context?.expectedStatus) {
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
          context.previousTicket
        );

        // Also rollback in all list caches
        const queryKeysToUpdate = [
          ['tickets', currentTenantUuid, 'list'],
          ['tickets', tenantId, 'list'],
          ['realtime-tickets', currentTenantUuid],
          ['realtime-tickets', tenantId],
        ];

        queryKeysToUpdate.forEach((queryKeyPattern) => {
          queryClient.invalidateQueries({
            queryKey: queryKeyPattern,
            exact: false,
          });
        });
      }

      // Show error toast
      toast.error('Failed to Send Reply', {
        description:
          error instanceof Error ? error.message : 'Please try again.',
        duration: 5000,
      });
    },
    onSettled: (data, error) => {
      const currentTenantUuid = tenantUuid || tenantId;

      if (!error && data?.message) {
        // On success, remove optimistic message and add the real one
        queryClient.setQueryData(
          QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
          (old: Array<{ id: string }> | undefined) => {
            if (!old) return [data.message];

            // Remove all optimistic messages and ensure the real message is present
            const filtered = old.filter(
              (msg) =>
                typeof msg.id === 'string' && !msg.id.startsWith('optimistic-')
            );

            // Check if the real message is already in the list (from real-time)
            const messageExists = filtered.some(
              (msg) => msg.id === data.message.id
            );
            if (!messageExists) {
              return [...filtered, data.message];
            }

            return filtered;
          }
        );
      } else if (error) {
        // On error, invalidate to restore previous state
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.messages(currentTenantUuid, ticketId),
        });
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        });
      }
    },
  });
};

// Simple status update mutation with optimistic updates
export const useUpdateTicketStatus = (tenantId: string) => {
  const queryClient = useQueryClient();
  const tenantUuidQuery = useTenantUuid(tenantId);
  const tenantUuid = tenantUuidQuery.data;

  return useMutation({
    mutationFn: async ({
      ticketId,
      newStatus,
      reason,
    }: {
      ticketId: string;
      newStatus: 'open' | 'pending' | 'resolved' | 'closed';
      reason?: string;
    }) => {
      const response = await fetch(`/api/tickets/${ticketId}/status`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          new_status: newStatus,
          reason,
          tenant_id: tenantId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to update status');
      }

      return response.json();
    },
    onMutate: async ({ ticketId, newStatus }) => {
      const currentTenantUuid = tenantUuid || tenantId;

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
      });

      // Snapshot previous data for rollback
      const previousTicket = queryClient.getQueryData(
        QueryKeys.TICKETS.detail(currentTenantUuid, ticketId)
      );
      const previousLists = new Map();

      // Optimistically update ticket detail cache
      queryClient.setQueryData(
        QueryKeys.TICKETS.detail(currentTenantUuid, ticketId),
        (old: Ticket | Ticket[] | undefined) => {
          const currentTicket = Array.isArray(old) ? old[0] : old;
          if (!currentTicket) return old;

          const updatedTicket = {
            ...currentTicket,
            status: newStatus,
            updatedAt: new Date(),
          };

          return Array.isArray(old) ? [updatedTicket] : updatedTicket;
        }
      );

      // Update all ticket list caches for instant UI feedback
      const queryKeysToUpdate = [
        ['tickets', currentTenantUuid, 'list'],
        ['tickets', tenantId, 'list'],
        ['realtime-tickets', currentTenantUuid],
        ['realtime-tickets', tenantId],
      ];

      // Store previous values and update all lists
      queryKeysToUpdate.forEach((queryKeyPattern) => {
        const queries = queryClient.getQueriesData({
          queryKey: queryKeyPattern,
          exact: false,
        });

        queries.forEach(([queryKey, data]) => {
          previousLists.set(queryKey, data);

          queryClient.setQueryData(queryKey, (old: Ticket[] | undefined) => {
            if (!Array.isArray(old)) return old;
            return old.map((ticket) =>
              ticket.id === ticketId
                ? { ...ticket, status: newStatus, updatedAt: new Date() }
                : ticket
            );
          });
        });
      });

      return { previousTicket, previousLists };
    },
    onSuccess: (_data, variables) => {
      toast.success('Status Updated', {
        description: `Ticket status changed to ${variables.newStatus}.`,
        duration: 3000,
      });
    },
    onError: (error, variables, context) => {
      const currentTenantUuid = tenantUuid || tenantId;

      // Rollback ticket detail on error
      if (context?.previousTicket) {
        queryClient.setQueryData(
          QueryKeys.TICKETS.detail(currentTenantUuid, variables.ticketId),
          context.previousTicket
        );
      }

      // Rollback all list caches on error
      if (context?.previousLists) {
        context.previousLists.forEach((data, queryKey) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      toast.error('Status Update Failed', {
        description:
          error instanceof Error ? error.message : 'Please try again.',
        duration: 5000,
      });
    },
    onSettled: (_data, error, variables) => {
      // Only invalidate on error to maintain optimistic updates
      if (error) {
        const currentTenantUuid = tenantUuid || tenantId;
        queryClient.invalidateQueries({
          queryKey: QueryKeys.TICKETS.detail(
            currentTenantUuid,
            variables.ticketId
          ),
        });
        queryClient.invalidateQueries({
          queryKey: ['tickets', currentTenantUuid, 'list'],
          exact: false,
        });
      }
    },
  });
};
